2025-05-24 11:59:22,641 - root - INFO - Settings loaded from settings.json
2025-05-24 11:59:55,215 - root - INFO - Settings loaded from settings.json
2025-05-24 12:00:39,255 - root - INFO - Settings loaded from settings.json
2025-05-24 12:00:42,295 - email_analyzer - INFO - Loaded email type classifier model from disk
2025-05-24 12:07:28,550 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:08:10,008 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:08:18,886 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:08:18,887 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:08:18,888 - root - INFO - Classifier initialized successfully
2025-05-24 12:08:19,335 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8080
2025-05-24 12:08:19,335 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-24 12:08:19,384 - werkzeug - INFO -  * Restarting with stat
2025-05-24 12:08:20,100 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:08:23,250 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:08:23,252 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:08:23,252 - root - INFO - Classifier initialized successfully
2025-05-24 12:08:23,270 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 12:08:23,288 - werkzeug - INFO -  * Debugger PIN: 624-016-730
2025-05-24 12:08:32,151 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:08:32] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-05-24 12:08:32,476 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:08:32] "GET /?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-05-24 12:08:32,535 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:08:32] "GET /?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-05-24 12:08:33,106 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:08:33] "GET /?__debugger__=yes&cmd=resource&f=console.png&s=qT2d5FDWXBEyg1jvgwkl HTTP/1.1" 200 -
2025-05-24 12:08:33,361 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:08:33] "GET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-05-24 12:12:49,900 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app\\routes\\auth.py', reloading
2025-05-24 12:12:50,696 - werkzeug - INFO -  * Restarting with stat
2025-05-24 12:12:51,636 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:13:03,959 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:13:06,865 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:13:06,867 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:13:06,868 - root - INFO - Classifier initialized successfully
2025-05-24 12:13:06,936 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8080
2025-05-24 12:13:06,936 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-24 12:13:06,942 - werkzeug - INFO -  * Restarting with stat
2025-05-24 12:13:07,487 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:13:10,415 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:13:10,417 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:13:10,417 - root - INFO - Classifier initialized successfully
2025-05-24 12:13:10,607 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 12:13:10,628 - werkzeug - INFO -  * Debugger PIN: 624-016-730
2025-05-24 12:13:23,308 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET / HTTP/1.1" 200 -
2025-05-24 12:13:23,592 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/css/animations.css HTTP/1.1" 200 -
2025-05-24 12:13:23,593 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/css/styles.css HTTP/1.1" 200 -
2025-05-24 12:13:23,743 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/css/visualization.css HTTP/1.1" 200 -
2025-05-24 12:13:23,744 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-24 12:13:23,745 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/css/hacker.css HTTP/1.1" 200 -
2025-05-24 12:13:23,750 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/js/matrix.js HTTP/1.1" 200 -
2025-05-24 12:13:23,928 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/js/gmail-integration.js HTTP/1.1" 200 -
2025-05-24 12:13:23,930 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/js/visualization.js HTTP/1.1" 200 -
2025-05-24 12:13:24,871 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:24] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-24 12:13:53,331 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:53] "GET /auth HTTP/1.1" 200 -
2025-05-24 12:13:53,565 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:53] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:13:53,898 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:53] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:13:53,944 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:53] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:13:53,988 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:53] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:14:28,885 - app.services.google_auth - WARNING - Direct password authentication is not implemented for security reasons
2025-05-24 12:14:28,897 - app.services.google_auth - WARNING - Using demo mode instead
2025-05-24 12:14:28,964 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:28] "POST /gmail-auth HTTP/1.1" 200 -
2025-05-24 12:14:29,136 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,152 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,468 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,470 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,498 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,533 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,535 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,608 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,803 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[33mGET /static/img/cyber-grid.png HTTP/1.1[0m" 404 -
2025-05-24 12:35:30,702 - app.services.google_auth - INFO - Running in demo mode
2025-05-24 12:35:30,718 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:30] "GET / HTTP/1.1" 200 -
2025-05-24 12:35:30,922 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:30] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:30,926 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:30] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:31,224 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:31] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:31,226 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:31] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:31,235 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:31] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:31,255 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:31] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:31,259 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:31] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:31,264 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:31] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:34,906 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:34] "GET /logout HTTP/1.1" 200 -
2025-05-24 12:35:35,264 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,547 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,576 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,608 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,673 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,675 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,681 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,927 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:47,113 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:47] "GET /auth HTTP/1.1" 200 -
2025-05-24 12:35:47,403 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:47] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:47,729 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:47] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:47,734 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:47] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:47,741 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:47] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:57,602 - app.services.google_auth - WARNING - Direct password authentication is not implemented for security reasons
2025-05-24 12:35:57,611 - app.services.google_auth - WARNING - Using demo mode instead
2025-05-24 12:35:57,629 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:57] "POST /gmail-auth HTTP/1.1" 200 -
2025-05-24 12:35:57,751 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:57] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:57,835 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:57] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:58,062 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:58] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:58,067 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:58] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:58,070 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:58] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:58,126 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:58] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:58,129 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:58] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:58,179 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:58] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 12:36:21,609 - app.services.google_auth - WARNING - Direct password authentication is not implemented for security reasons
2025-05-24 12:36:21,610 - app.services.google_auth - WARNING - Using demo mode instead
2025-05-24 12:36:21,614 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:21] "POST /gmail-auth HTTP/1.1" 200 -
2025-05-24 12:36:21,750 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:21] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:36:21,852 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:21] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:36:22,096 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:22] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:36:22,101 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:22] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 12:36:22,104 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:22] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 12:36:22,141 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:22] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:36:22,194 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:22] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 12:36:22,196 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:22] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 12:45:51,798 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app\\routes\\auth.py', reloading
2025-05-24 12:45:53,900 - werkzeug - INFO -  * Restarting with stat
2025-05-24 12:45:54,726 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:45:59,429 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:45:59,430 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:45:59,431 - root - INFO - Classifier initialized successfully
2025-05-24 12:45:59,448 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 12:45:59,459 - werkzeug - INFO -  * Debugger PIN: 624-016-730
2025-05-24 12:46:11,030 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app\\routes\\auth.py', reloading
2025-05-24 12:46:11,386 - werkzeug - INFO -  * Restarting with stat
2025-05-24 12:46:12,122 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:46:14,762 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:46:14,763 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:46:14,763 - root - INFO - Classifier initialized successfully
2025-05-24 12:46:14,777 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 12:46:14,780 - werkzeug - INFO -  * Debugger PIN: 624-016-730
2025-05-24 12:46:55,345 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app\\routes\\api.py', reloading
2025-05-24 12:46:55,612 - werkzeug - INFO -  * Restarting with stat
2025-05-24 12:46:56,030 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:46:58,316 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:46:58,321 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:46:58,322 - root - INFO - Classifier initialized successfully
2025-05-24 12:46:58,346 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 12:46:58,353 - werkzeug - INFO -  * Debugger PIN: 624-016-730
2025-05-24 12:48:03,355 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:48:05,561 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:48:05,562 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:48:05,562 - root - INFO - Classifier initialized successfully
2025-05-24 12:48:05,610 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8080
2025-05-24 12:48:05,611 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-24 12:48:05,613 - werkzeug - INFO -  * Restarting with stat
2025-05-24 12:48:06,002 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:48:08,269 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:48:08,270 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:48:08,271 - root - INFO - Classifier initialized successfully
2025-05-24 12:48:08,286 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 12:48:08,291 - werkzeug - INFO -  * Debugger PIN: 624-016-730
2025-05-24 12:48:18,712 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:18] "GET / HTTP/1.1" 200 -
2025-05-24 12:48:18,914 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:18] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:18,915 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:18] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:19,089 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:19] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:19,091 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:19] "GET /static/css/hacker.css HTTP/1.1" 200 -
2025-05-24 12:48:19,102 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:19] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 12:48:19,109 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:19] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:48:19,248 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:19] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 12:48:19,256 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:19] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 12:48:21,428 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:21] "GET /auth HTTP/1.1" 200 -
2025-05-24 12:48:21,729 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:21] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:21,943 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:21] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:21,951 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:21] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:21,965 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:21] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:48:35,280 - app.services.google_auth - WARNING - Direct password authentication is not implemented for security reasons
2025-05-24 12:48:35,290 - app.services.google_auth - WARNING - Using demo mode instead
2025-05-24 12:48:35,547 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:35] "POST /gmail-auth HTTP/1.1" 200 -
2025-05-24 12:48:35,719 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:35] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:35,721 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:35] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:36,039 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:36] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:36,042 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:36] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:36,061 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:36] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 12:48:36,076 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:36] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:48:36,107 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:36] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 12:48:36,127 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:36] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 12:48:36,389 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:36] "[33mGET /static/img/cyber-grid.png HTTP/1.1[0m" 404 -
2025-05-24 13:44:58,451 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 13:45:03,147 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 13:45:03,148 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 13:45:03,148 - root - INFO - Classifier initialized successfully
2025-05-24 13:45:03,189 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8080
2025-05-24 13:45:03,190 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-24 13:45:03,195 - werkzeug - INFO -  * Restarting with stat
2025-05-24 13:45:03,634 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 13:45:05,977 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 13:45:05,978 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 13:45:05,979 - root - INFO - Classifier initialized successfully
2025-05-24 13:45:05,989 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 13:45:06,000 - werkzeug - INFO -  * Debugger PIN: 624-016-730
2025-05-24 13:45:23,677 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:23] "GET / HTTP/1.1" 200 -
2025-05-24 13:45:23,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:23] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 13:45:23,890 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:23] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 13:45:24,055 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:24] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 13:45:24,057 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:24] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 13:45:24,060 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:24] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 13:45:24,066 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:24] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 13:45:24,200 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:24] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 13:45:24,206 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:24] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 13:45:25,707 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:25] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-24 14:14:32,625 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:32] "GET /auth HTTP/1.1" 200 -
2025-05-24 14:14:33,080 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:33] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 14:14:33,421 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:33] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 14:14:33,439 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:33] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 14:14:33,432 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:33] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 14:14:37,802 - app.services.google_auth - ERROR - Client secrets file not found: client_secret.json
2025-05-24 14:14:37,874 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:37] "[32mGET /login HTTP/1.1[0m" 302 -
2025-05-24 14:14:38,044 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:38] "GET /demo-auth HTTP/1.1" 200 -
2025-05-24 14:14:38,136 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:38] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 14:14:38,463 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:38] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 14:14:38,466 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:38] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 14:14:38,466 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:38] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 14:14:38,471 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:38] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 14:14:38,477 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:38] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 14:14:38,518 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:38] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 14:14:38,789 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:38] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 14:15:29,085 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:15:29] "GET /api/emails HTTP/1.1" 200 -
2025-05-24 14:15:29,098 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:15:29] "GET /api/emails HTTP/1.1" 200 -
2025-05-24 14:16:09,638 - app.services.email_analyzer - INFO - Loaded email type classifier model from disk
2025-05-24 14:16:09,669 - app.services.email_analyzer - ERROR - Error analyzing email: 
**********************************************************************
  Resource [93mpunkt_tab[0m not found.
  Please use the NLTK Downloader to obtain the resource:

  [31m>>> import nltk
  >>> nltk.download('punkt_tab')
  [0m
  For more information see: https://www.nltk.org/data.html

  Attempted to load [93mtokenizers/punkt_tab/english/[0m

  Searched in:
    - 'C:\\Users\\<USER>\\Users\\Suresh Kumar\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\.venv\\nltk_data'
    - 'c:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\.venv\\share\\nltk_data'
    - 'c:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\.venv\\lib\\nltk_data'
    - 'C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data'
    - 'C:\\nltk_data'
    - 'D:\\nltk_data'
    - 'E:\\nltk_data'
**********************************************************************

2025-05-24 14:16:09,673 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:16:09] "POST /api/analyze HTTP/1.1" 200 -
2025-05-24 14:16:26,289 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:16:26] "GET /api/emails HTTP/1.1" 200 -
2025-05-24 14:16:27,904 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:16:27] "GET /api/emails HTTP/1.1" 200 -
2025-05-24 14:16:28,651 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:16:28] "GET /api/emails HTTP/1.1" 200 -
2025-05-24 14:16:28,896 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:16:28] "GET /api/emails HTTP/1.1" 200 -
2025-05-24 14:16:28,971 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:16:28] "GET /api/emails HTTP/1.1" 200 -
2025-05-24 14:16:29,248 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:16:29] "GET /api/emails HTTP/1.1" 200 -
2025-05-24 14:16:29,983 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:16:29] "GET /api/emails HTTP/1.1" 200 -
2025-05-24 14:17:24,446 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:24] "GET /logout HTTP/1.1" 200 -
2025-05-24 14:17:24,751 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:24] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 14:17:24,966 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:24] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 14:17:24,980 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:24] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 14:17:24,982 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:24] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 14:17:24,984 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:24] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 14:17:25,022 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:25] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 14:17:25,124 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:25] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 14:17:25,285 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:25] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 14:17:28,527 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:28] "GET /auth HTTP/1.1" 200 -
2025-05-24 14:17:28,798 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:28] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 14:17:28,969 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:28] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 14:17:28,969 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:28] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 14:17:28,973 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:28] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 14:18:22,267 - app.services.google_auth - WARNING - Direct password authentication is not implemented for security reasons
2025-05-24 14:18:22,268 - app.services.google_auth - WARNING - Using demo mode instead
2025-05-24 14:18:22,278 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:18:22] "POST /gmail-auth HTTP/1.1" 200 -
2025-05-24 14:18:22,396 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:18:22] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 14:18:22,498 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:18:22] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 14:18:22,716 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:18:22] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 14:18:22,721 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:18:22] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 14:18:22,721 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:18:22] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 14:18:22,728 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:18:22] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 14:18:22,739 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:18:22] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 14:18:22,814 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:18:22] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 17:33:56,396 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 17:34:19,191 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 17:34:38,680 - app.services.email_analyzer - INFO - Loaded email type classifier model from disk
2025-05-24 17:34:38,687 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:34:38,687 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:34:38,690 - __main__ - INFO - Classifier initialized successfully
2025-05-24 17:34:38,793 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8080
2025-05-24 17:34:38,793 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-24 17:34:38,838 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-05-24 17:34:39,197 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 17:34:41,930 - app.services.email_analyzer - INFO - Loaded email type classifier model from disk
2025-05-24 17:34:41,930 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:34:41,930 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:34:41,930 - __main__ - INFO - Classifier initialized successfully
2025-05-24 17:34:41,950 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 17:34:41,967 - werkzeug - INFO -  * Debugger PIN: 309-126-132
2025-05-24 17:34:57,676 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py', reloading
2025-05-24 17:34:57,741 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py', reloading
2025-05-24 17:34:57,805 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py', reloading
2025-05-24 17:34:57,859 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py', reloading
2025-05-24 17:34:57,876 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:34:57] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-05-24 17:34:58,012 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:34:58] "[36mGET /?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-24 17:34:58,696 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-05-24 17:34:59,406 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 17:35:02,656 - app.services.email_analyzer - INFO - Loaded email type classifier model from disk
2025-05-24 17:35:02,659 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:35:02,660 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:35:02,660 - __main__ - INFO - Classifier initialized successfully
2025-05-24 17:35:02,677 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 17:35:02,690 - werkzeug - INFO -  * Debugger PIN: 309-126-132
2025-05-24 17:35:02,768 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:35:02] "[36mGET /?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-24 17:35:03,150 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:35:03] "GET /?__debugger__=yes&cmd=resource&f=console.png&s=mkyroElmobIJgXdFI5o1 HTTP/1.1" 200 -
2025-05-24 17:35:18,244 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:35:18] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-05-24 17:35:18,290 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:35:18] "[36mGET /?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-24 17:35:18,474 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:35:18] "[36mGET /?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-24 17:35:18,833 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:35:18] "GET /?__debugger__=yes&cmd=resource&f=console.png&s=IVBAaWsDa4lzDrLDKSaa HTTP/1.1" 200 -
2025-05-24 17:35:19,104 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:35:19] "[36mGET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-24 17:35:26,473 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:35:26,476 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:35:27,419 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-05-24 17:35:27,990 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 17:35:31,200 - app.services.email_analyzer - INFO - Loaded email type classifier model from disk
2025-05-24 17:35:31,200 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:35:31,200 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:35:31,200 - __main__ - INFO - Classifier initialized successfully
2025-05-24 17:35:31,224 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 17:35:31,224 - werkzeug - INFO -  * Debugger PIN: 309-126-132
2025-05-24 17:35:39,551 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:35:39,553 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:35:40,733 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-05-24 17:35:41,165 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 17:35:46,012 - app.services.email_analyzer - INFO - Loaded email type classifier model from disk
2025-05-24 17:35:46,017 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:35:46,017 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:35:46,024 - __main__ - INFO - Classifier initialized successfully
2025-05-24 17:35:46,043 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 17:35:46,048 - werkzeug - INFO -  * Debugger PIN: 309-126-132
2025-05-24 17:36:05,021 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:36:05,021 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:36:05,025 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:36:05,025 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:36:05,707 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-05-24 17:36:06,151 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 17:36:09,693 - app.services.email_analyzer - INFO - Loaded email type classifier model from disk
2025-05-24 17:36:09,699 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:36:09,699 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:36:09,702 - __main__ - INFO - Classifier initialized successfully
2025-05-24 17:36:09,718 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 17:36:09,722 - werkzeug - INFO -  * Debugger PIN: 309-126-132
2025-05-24 17:36:26,314 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:36:26,320 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:36:26,320 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:36:27,216 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-05-24 17:36:27,599 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 17:36:30,682 - app.services.email_analyzer - INFO - Loaded email type classifier model from disk
2025-05-24 17:36:30,682 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:36:30,682 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:36:30,682 - __main__ - INFO - Classifier initialized successfully
2025-05-24 17:36:30,699 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 17:36:30,706 - werkzeug - INFO -  * Debugger PIN: 309-126-132
2025-05-24 17:36:50,897 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:36:50,898 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:36:50,898 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:36:50,899 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:36:51,269 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-05-24 17:36:51,592 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 17:36:54,562 - app.services.email_analyzer - INFO - Loaded email type classifier model from disk
2025-05-24 17:36:54,566 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:36:54,569 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:36:54,571 - __main__ - INFO - Classifier initialized successfully
2025-05-24 17:36:54,588 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 17:36:54,591 - werkzeug - INFO -  * Debugger PIN: 309-126-132
2025-05-24 17:37:18,294 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:37:18,296 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:37:18,296 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:37:19,059 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-05-24 17:37:19,420 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 17:37:22,443 - app.services.email_analyzer - INFO - Loaded email type classifier model from disk
2025-05-24 17:37:22,453 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:37:22,453 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:37:22,453 - __main__ - INFO - Classifier initialized successfully
2025-05-24 17:37:22,469 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 17:37:22,485 - werkzeug - INFO -  * Debugger PIN: 309-126-132
2025-05-24 17:37:49,344 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:37:49,352 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:37:49,353 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:37:49,353 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app.py', reloading
2025-05-24 17:37:50,012 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-05-24 17:37:50,448 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 17:37:53,521 - app.services.email_analyzer - INFO - Loaded email type classifier model from disk
2025-05-24 17:37:53,522 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:37:53,523 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:37:53,525 - __main__ - INFO - Classifier initialized successfully
2025-05-24 17:37:53,538 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 17:37:53,542 - werkzeug - INFO -  * Debugger PIN: 309-126-132
2025-05-24 17:38:41,012 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 17:38:43,713 - app.services.email_analyzer - INFO - Loaded email type classifier model from disk
2025-05-24 17:38:43,715 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:38:43,717 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:38:43,718 - __main__ - INFO - Classifier initialized successfully
2025-05-24 17:38:43,755 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8080
2025-05-24 17:38:43,755 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-24 17:38:43,774 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-05-24 17:38:44,127 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 17:38:47,350 - app.services.email_analyzer - INFO - Loaded email type classifier model from disk
2025-05-24 17:38:47,350 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:38:47,350 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:38:47,350 - __main__ - INFO - Classifier initialized successfully
2025-05-24 17:38:47,380 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 17:38:47,384 - werkzeug - INFO -  * Debugger PIN: 309-126-132
2025-05-24 17:39:04,974 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:39:04] "GET / HTTP/1.1" 200 -
2025-05-24 17:39:05,085 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:39:05] "GET /static/css/styles.css HTTP/1.1" 200 -
2025-05-24 17:39:05,232 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:39:05] "GET /static/css/animations.css HTTP/1.1" 200 -
2025-05-24 17:39:05,341 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:39:05] "GET /static/css/hacker.css HTTP/1.1" 200 -
2025-05-24 17:39:05,342 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:39:05] "GET /static/css/visualization.css HTTP/1.1" 200 -
2025-05-24 17:39:05,346 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:39:05] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-24 17:39:05,365 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:39:05] "GET /static/js/matrix.js HTTP/1.1" 200 -
2025-05-24 17:39:05,406 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:39:05] "GET /static/js/gmail-integration.js HTTP/1.1" 200 -
2025-05-24 17:39:05,548 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:39:05] "GET /static/js/visualization.js HTTP/1.1" 200 -
2025-05-24 17:39:05,898 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:39:05] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-24 17:39:10,246 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:39:10] "GET /auth HTTP/1.1" 200 -
2025-05-24 17:39:10,500 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:39:10] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 17:39:10,672 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:39:10] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 17:39:10,672 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:39:10] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 17:39:10,680 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:39:10] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 17:46:53,462 - app.services.google_auth - ERROR - Client secrets file not found: client_secret.json
2025-05-24 17:46:53,465 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:46:53] "[32mGET /login HTTP/1.1[0m" 302 -
2025-05-24 17:46:53,730 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:46:53] "GET /demo-auth HTTP/1.1" 200 -
2025-05-24 17:46:54,116 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:46:54] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 17:46:54,122 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:46:54] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 17:46:54,145 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:46:54] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 17:46:54,152 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:46:54] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 17:46:54,171 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:46:54] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 17:46:54,178 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:46:54] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 17:46:54,459 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:46:54] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 17:46:54,460 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:46:54] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 17:46:57,908 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:46:57] "GET /logout HTTP/1.1" 200 -
2025-05-24 17:46:58,140 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:46:58] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 17:46:58,155 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:46:58] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 17:46:58,317 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:46:58] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 17:46:58,317 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:46:58] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 17:46:58,319 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:46:58] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 17:46:58,328 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:46:58] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 17:46:58,458 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:46:58] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 17:46:58,472 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:46:58] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 17:47:00,861 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:47:00] "GET /auth HTTP/1.1" 200 -
2025-05-24 17:47:01,092 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:47:01] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 17:47:01,144 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:47:01] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 17:47:01,247 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:47:01] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 17:47:01,247 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:47:01] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 17:48:50,006 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:48:50] "GET / HTTP/1.1" 200 -
2025-05-24 17:48:50,010 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:48:50] "GET / HTTP/1.1" 200 -
2025-05-24 17:48:50,250 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:48:50] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 17:48:50,380 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:48:50] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 17:48:50,380 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:48:50] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 17:48:50,380 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:48:50] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 17:48:50,392 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:48:50] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 17:48:50,407 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:48:50] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 17:48:50,567 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:48:50] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 17:48:50,709 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:48:50] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 17:48:50,710 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:48:50] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 17:48:50,714 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:48:50] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 17:48:50,722 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:48:50] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 17:48:50,746 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:48:50] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 17:48:50,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:48:50] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-24 17:48:51,044 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:48:51] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 17:48:51,047 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:48:51] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 17:48:51,140 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:48:51] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 17:48:51,375 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:48:51] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 17:51:00,445 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 17:51:04,418 - app.services.email_analyzer - INFO - Loaded email type classifier model from disk
2025-05-24 17:51:04,418 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:51:04,418 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:51:04,418 - __main__ - INFO - Classifier initialized successfully
2025-05-24 17:51:04,469 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:5000
2025-05-24 17:51:04,469 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-24 17:51:04,499 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-05-24 17:51:04,897 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 17:51:07,789 - app.services.email_analyzer - INFO - Loaded email type classifier model from disk
2025-05-24 17:51:07,790 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:51:07,793 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 17:51:07,794 - __main__ - INFO - Classifier initialized successfully
2025-05-24 17:51:07,816 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 17:51:07,819 - werkzeug - INFO -  * Debugger PIN: 309-126-132
2025-05-24 17:51:24,792 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:51:24] "GET / HTTP/1.1" 200 -
2025-05-24 17:51:24,902 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:51:24] "GET /static/css/styles.css HTTP/1.1" 200 -
2025-05-24 17:51:25,035 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:51:25] "GET /static/css/animations.css HTTP/1.1" 200 -
2025-05-24 17:51:25,149 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:51:25] "GET /static/css/hacker.css HTTP/1.1" 200 -
2025-05-24 17:51:25,149 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:51:25] "GET /static/css/visualization.css HTTP/1.1" 200 -
2025-05-24 17:51:25,149 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:51:25] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-24 17:51:25,149 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:51:25] "GET /static/js/matrix.js HTTP/1.1" 200 -
2025-05-24 17:51:25,217 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:51:25] "GET /static/js/gmail-integration.js HTTP/1.1" 200 -
2025-05-24 17:51:25,373 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:51:25] "GET /static/js/visualization.js HTTP/1.1" 200 -
2025-05-24 17:51:25,824 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 17:51:25] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
