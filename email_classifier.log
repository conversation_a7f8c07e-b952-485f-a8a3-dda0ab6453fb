2025-05-24 11:59:22,641 - root - INFO - Settings loaded from settings.json
2025-05-24 11:59:55,215 - root - INFO - Settings loaded from settings.json
2025-05-24 12:00:39,255 - root - INFO - Settings loaded from settings.json
2025-05-24 12:00:42,295 - email_analyzer - INFO - Loaded email type classifier model from disk
2025-05-24 12:07:28,550 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:08:10,008 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:08:18,886 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:08:18,887 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:08:18,888 - root - INFO - Classifier initialized successfully
2025-05-24 12:08:19,335 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8080
2025-05-24 12:08:19,335 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-24 12:08:19,384 - werkzeug - INFO -  * Restarting with stat
2025-05-24 12:08:20,100 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:08:23,250 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:08:23,252 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:08:23,252 - root - INFO - Classifier initialized successfully
2025-05-24 12:08:23,270 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 12:08:23,288 - werkzeug - INFO -  * Debugger PIN: 624-016-730
2025-05-24 12:08:32,151 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:08:32] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-05-24 12:08:32,476 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:08:32] "GET /?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-05-24 12:08:32,535 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:08:32] "GET /?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-05-24 12:08:33,106 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:08:33] "GET /?__debugger__=yes&cmd=resource&f=console.png&s=qT2d5FDWXBEyg1jvgwkl HTTP/1.1" 200 -
2025-05-24 12:08:33,361 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:08:33] "GET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-05-24 12:12:49,900 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app\\routes\\auth.py', reloading
2025-05-24 12:12:50,696 - werkzeug - INFO -  * Restarting with stat
2025-05-24 12:12:51,636 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:13:03,959 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:13:06,865 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:13:06,867 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:13:06,868 - root - INFO - Classifier initialized successfully
2025-05-24 12:13:06,936 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8080
2025-05-24 12:13:06,936 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-24 12:13:06,942 - werkzeug - INFO -  * Restarting with stat
2025-05-24 12:13:07,487 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:13:10,415 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:13:10,417 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:13:10,417 - root - INFO - Classifier initialized successfully
2025-05-24 12:13:10,607 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 12:13:10,628 - werkzeug - INFO -  * Debugger PIN: 624-016-730
2025-05-24 12:13:23,308 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET / HTTP/1.1" 200 -
2025-05-24 12:13:23,592 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/css/animations.css HTTP/1.1" 200 -
2025-05-24 12:13:23,593 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/css/styles.css HTTP/1.1" 200 -
2025-05-24 12:13:23,743 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/css/visualization.css HTTP/1.1" 200 -
2025-05-24 12:13:23,744 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-24 12:13:23,745 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/css/hacker.css HTTP/1.1" 200 -
2025-05-24 12:13:23,750 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/js/matrix.js HTTP/1.1" 200 -
2025-05-24 12:13:23,928 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/js/gmail-integration.js HTTP/1.1" 200 -
2025-05-24 12:13:23,930 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/js/visualization.js HTTP/1.1" 200 -
2025-05-24 12:13:24,871 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:24] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-24 12:13:53,331 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:53] "GET /auth HTTP/1.1" 200 -
2025-05-24 12:13:53,565 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:53] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:13:53,898 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:53] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:13:53,944 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:53] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:13:53,988 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:53] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:14:28,885 - app.services.google_auth - WARNING - Direct password authentication is not implemented for security reasons
2025-05-24 12:14:28,897 - app.services.google_auth - WARNING - Using demo mode instead
2025-05-24 12:14:28,964 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:28] "POST /gmail-auth HTTP/1.1" 200 -
2025-05-24 12:14:29,136 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,152 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,468 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,470 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,498 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,533 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,535 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,608 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,803 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[33mGET /static/img/cyber-grid.png HTTP/1.1[0m" 404 -
2025-05-24 12:35:30,702 - app.services.google_auth - INFO - Running in demo mode
2025-05-24 12:35:30,718 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:30] "GET / HTTP/1.1" 200 -
2025-05-24 12:35:30,922 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:30] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:30,926 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:30] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:31,224 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:31] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:31,226 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:31] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:31,235 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:31] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:31,255 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:31] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:31,259 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:31] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:31,264 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:31] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:34,906 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:34] "GET /logout HTTP/1.1" 200 -
2025-05-24 12:35:35,264 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,547 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,576 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,608 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,673 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,675 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,681 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,927 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:47,113 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:47] "GET /auth HTTP/1.1" 200 -
2025-05-24 12:35:47,403 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:47] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:47,729 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:47] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:47,734 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:47] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:47,741 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:47] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:57,602 - app.services.google_auth - WARNING - Direct password authentication is not implemented for security reasons
2025-05-24 12:35:57,611 - app.services.google_auth - WARNING - Using demo mode instead
2025-05-24 12:35:57,629 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:57] "POST /gmail-auth HTTP/1.1" 200 -
2025-05-24 12:35:57,751 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:57] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:57,835 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:57] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:58,062 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:58] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:58,067 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:58] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:58,070 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:58] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:58,126 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:58] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:58,129 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:58] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:58,179 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:58] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 12:36:21,609 - app.services.google_auth - WARNING - Direct password authentication is not implemented for security reasons
2025-05-24 12:36:21,610 - app.services.google_auth - WARNING - Using demo mode instead
2025-05-24 12:36:21,614 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:21] "POST /gmail-auth HTTP/1.1" 200 -
2025-05-24 12:36:21,750 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:21] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:36:21,852 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:21] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:36:22,096 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:22] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:36:22,101 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:22] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 12:36:22,104 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:22] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 12:36:22,141 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:22] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:36:22,194 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:22] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 12:36:22,196 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:22] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 12:45:51,798 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app\\routes\\auth.py', reloading
2025-05-24 12:45:53,900 - werkzeug - INFO -  * Restarting with stat
2025-05-24 12:45:54,726 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:45:59,429 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:45:59,430 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:45:59,431 - root - INFO - Classifier initialized successfully
2025-05-24 12:45:59,448 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 12:45:59,459 - werkzeug - INFO -  * Debugger PIN: 624-016-730
2025-05-24 12:46:11,030 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app\\routes\\auth.py', reloading
2025-05-24 12:46:11,386 - werkzeug - INFO -  * Restarting with stat
2025-05-24 12:46:12,122 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:46:14,762 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:46:14,763 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:46:14,763 - root - INFO - Classifier initialized successfully
2025-05-24 12:46:14,777 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 12:46:14,780 - werkzeug - INFO -  * Debugger PIN: 624-016-730
2025-05-24 12:46:55,345 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app\\routes\\api.py', reloading
2025-05-24 12:46:55,612 - werkzeug - INFO -  * Restarting with stat
2025-05-24 12:46:56,030 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:46:58,316 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:46:58,321 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:46:58,322 - root - INFO - Classifier initialized successfully
2025-05-24 12:46:58,346 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 12:46:58,353 - werkzeug - INFO -  * Debugger PIN: 624-016-730
2025-05-24 12:48:03,355 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:48:05,561 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:48:05,562 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:48:05,562 - root - INFO - Classifier initialized successfully
2025-05-24 12:48:05,610 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8080
2025-05-24 12:48:05,611 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-24 12:48:05,613 - werkzeug - INFO -  * Restarting with stat
2025-05-24 12:48:06,002 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:48:08,269 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:48:08,270 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:48:08,271 - root - INFO - Classifier initialized successfully
2025-05-24 12:48:08,286 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 12:48:08,291 - werkzeug - INFO -  * Debugger PIN: 624-016-730
2025-05-24 12:48:18,712 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:18] "GET / HTTP/1.1" 200 -
2025-05-24 12:48:18,914 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:18] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:18,915 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:18] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:19,089 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:19] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:19,091 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:19] "GET /static/css/hacker.css HTTP/1.1" 200 -
2025-05-24 12:48:19,102 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:19] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 12:48:19,109 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:19] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:48:19,248 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:19] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 12:48:19,256 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:19] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 12:48:21,428 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:21] "GET /auth HTTP/1.1" 200 -
2025-05-24 12:48:21,729 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:21] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:21,943 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:21] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:21,951 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:21] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:21,965 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:21] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:48:35,280 - app.services.google_auth - WARNING - Direct password authentication is not implemented for security reasons
2025-05-24 12:48:35,290 - app.services.google_auth - WARNING - Using demo mode instead
2025-05-24 12:48:35,547 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:35] "POST /gmail-auth HTTP/1.1" 200 -
2025-05-24 12:48:35,719 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:35] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:35,721 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:35] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:36,039 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:36] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:36,042 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:36] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 12:48:36,061 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:36] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 12:48:36,076 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:36] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:48:36,107 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:36] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 12:48:36,127 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:36] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 12:48:36,389 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:48:36] "[33mGET /static/img/cyber-grid.png HTTP/1.1[0m" 404 -
2025-05-24 13:44:58,451 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 13:45:03,147 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 13:45:03,148 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 13:45:03,148 - root - INFO - Classifier initialized successfully
2025-05-24 13:45:03,189 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8080
2025-05-24 13:45:03,190 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-24 13:45:03,195 - werkzeug - INFO -  * Restarting with stat
2025-05-24 13:45:03,634 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 13:45:05,977 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 13:45:05,978 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 13:45:05,979 - root - INFO - Classifier initialized successfully
2025-05-24 13:45:05,989 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 13:45:06,000 - werkzeug - INFO -  * Debugger PIN: 624-016-730
2025-05-24 13:45:23,677 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:23] "GET / HTTP/1.1" 200 -
2025-05-24 13:45:23,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:23] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 13:45:23,890 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:23] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 13:45:24,055 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:24] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 13:45:24,057 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:24] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 13:45:24,060 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:24] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 13:45:24,066 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:24] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 13:45:24,200 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:24] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 13:45:24,206 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:24] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 13:45:25,707 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 13:45:25] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-24 14:14:32,625 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:32] "GET /auth HTTP/1.1" 200 -
2025-05-24 14:14:33,080 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:33] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 14:14:33,421 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:33] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 14:14:33,439 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:33] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 14:14:33,432 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:33] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 14:14:37,802 - app.services.google_auth - ERROR - Client secrets file not found: client_secret.json
2025-05-24 14:14:37,874 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:37] "[32mGET /login HTTP/1.1[0m" 302 -
2025-05-24 14:14:38,044 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:38] "GET /demo-auth HTTP/1.1" 200 -
2025-05-24 14:14:38,136 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:38] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 14:14:38,463 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:38] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 14:14:38,466 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:38] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 14:14:38,466 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:38] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 14:14:38,471 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:38] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 14:14:38,477 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:38] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 14:14:38,518 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:38] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 14:14:38,789 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:14:38] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 14:15:29,085 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:15:29] "GET /api/emails HTTP/1.1" 200 -
2025-05-24 14:15:29,098 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:15:29] "GET /api/emails HTTP/1.1" 200 -
2025-05-24 14:16:09,638 - app.services.email_analyzer - INFO - Loaded email type classifier model from disk
2025-05-24 14:16:09,669 - app.services.email_analyzer - ERROR - Error analyzing email: 
**********************************************************************
  Resource [93mpunkt_tab[0m not found.
  Please use the NLTK Downloader to obtain the resource:

  [31m>>> import nltk
  >>> nltk.download('punkt_tab')
  [0m
  For more information see: https://www.nltk.org/data.html

  Attempted to load [93mtokenizers/punkt_tab/english/[0m

  Searched in:
    - 'C:\\Users\\<USER>\\Users\\Suresh Kumar\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\.venv\\nltk_data'
    - 'c:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\.venv\\share\\nltk_data'
    - 'c:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\.venv\\lib\\nltk_data'
    - 'C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data'
    - 'C:\\nltk_data'
    - 'D:\\nltk_data'
    - 'E:\\nltk_data'
**********************************************************************

2025-05-24 14:16:09,673 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:16:09] "POST /api/analyze HTTP/1.1" 200 -
2025-05-24 14:16:26,289 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:16:26] "GET /api/emails HTTP/1.1" 200 -
2025-05-24 14:16:27,904 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:16:27] "GET /api/emails HTTP/1.1" 200 -
2025-05-24 14:16:28,651 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:16:28] "GET /api/emails HTTP/1.1" 200 -
2025-05-24 14:16:28,896 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:16:28] "GET /api/emails HTTP/1.1" 200 -
2025-05-24 14:16:28,971 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:16:28] "GET /api/emails HTTP/1.1" 200 -
2025-05-24 14:16:29,248 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:16:29] "GET /api/emails HTTP/1.1" 200 -
2025-05-24 14:16:29,983 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:16:29] "GET /api/emails HTTP/1.1" 200 -
2025-05-24 14:17:24,446 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:24] "GET /logout HTTP/1.1" 200 -
2025-05-24 14:17:24,751 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:24] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 14:17:24,966 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:24] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 14:17:24,980 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:24] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 14:17:24,982 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:24] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 14:17:24,984 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:24] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 14:17:25,022 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:25] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 14:17:25,124 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:25] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 14:17:25,285 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:25] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 14:17:28,527 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:28] "GET /auth HTTP/1.1" 200 -
2025-05-24 14:17:28,798 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:28] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 14:17:28,969 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:28] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 14:17:28,969 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:28] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 14:17:28,973 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:17:28] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 14:18:22,267 - app.services.google_auth - WARNING - Direct password authentication is not implemented for security reasons
2025-05-24 14:18:22,268 - app.services.google_auth - WARNING - Using demo mode instead
2025-05-24 14:18:22,278 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:18:22] "POST /gmail-auth HTTP/1.1" 200 -
2025-05-24 14:18:22,396 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:18:22] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 14:18:22,498 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:18:22] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 14:18:22,716 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:18:22] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 14:18:22,721 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:18:22] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 14:18:22,721 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:18:22] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 14:18:22,728 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:18:22] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 14:18:22,739 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:18:22] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 14:18:22,814 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 14:18:22] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
