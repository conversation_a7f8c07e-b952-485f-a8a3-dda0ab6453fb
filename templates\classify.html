<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Classify Email - Live Email Classifier</title>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Rajdhani:wght@300;400;500;600;700&family=Share+Tech+Mono&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../static/css/styles.css">
    <link rel="stylesheet" href="../static/css/animations.css">
</head>
<body>
    <!-- Scanline Effect -->
    <div class="scanline"></div>
    
    <!-- Header -->
    <header class="header">
        <div class="logo">
            <img src="../static/img/logo.png" alt="Email Classifier Logo">
            <span class="logo-text neon-pulse">LIVE EMAIL CLASSIFIER</span>
        </div>
        
        <div class="header-actions">
            <button id="theme-toggle" class="btn btn-outline">
                <i class="fas fa-moon"></i> Dark Mode
            </button>
            <button class="menu-toggle">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </div>
    </header>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="user-info">
                <div class="user-avatar cyber-border">
                    <img src="../static/img/user-avatar.jpg" alt="User Avatar">
                </div>
                <h3 class="user-name">NETRUNNER</h3>
                <p class="user-role">Email Admin</p>
            </div>
            
            <nav class="nav-menu">
                <ul>
                    <li class="nav-item">
                        <a href="index.html" class="nav-link">
                            <i class="fas fa-home"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="emails.html" class="nav-link">
                            <i class="fas fa-envelope"></i> Emails
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="classify.html" class="nav-link active">
                            <i class="fas fa-filter"></i> Classify
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="settings.html" class="nav-link">
                            <i class="fas fa-cog"></i> Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="about.html" class="nav-link">
                            <i class="fas fa-info-circle"></i> About
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <div class="terminal">
                    <div class="terminal-header">
                        <span class="terminal-title">SYSTEM STATUS</span>
                        <div class="terminal-controls">
                            <span class="control"></span>
                            <span class="control"></span>
                            <span class="control"></span>
                        </div>
                    </div>
                    <div class="terminal-content"></div>
                </div>
            </div>
        </aside>
        
        <!-- Content Area -->
        <div class="content">
            <div class="classify-container">
                <div class="page-header">
                    <h1 class="glitch" data-text="CLASSIFY EMAIL">CLASSIFY EMAIL</h1>
                    <p class="subtitle">Test the email classification system</p>
                </div>
                
                <!-- Classification Form -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">Email Classification</h2>
                    </div>
                    <div class="card-body">
                        <form id="classify-form" class="cyber-form">
                            <div class="form-group">
                                <label for="email-text">Email Content</label>
                                <textarea id="email-text" name="email-text" rows="10" placeholder="Paste email content here..." class="cyber-input"></textarea>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter"></i> Classify Email
                                </button>
                                <button type="reset" class="btn btn-outline">
                                    <i class="fas fa-redo"></i> Reset
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Classification Result -->
                <div class="classification-result" style="display: none;">
                    <!-- Content will be dynamically added by JavaScript -->
                </div>
                
                <!-- Sample Emails -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">Sample Emails</h2>
                    </div>
                    <div class="card-body">
                        <div class="sample-emails">
                            <div class="sample-email-card cyber-border" data-category="Spam">
                                <div class="sample-email-header">
                                    <h3>Spam Example</h3>
                                    <span class="sample-category" style="color: var(--glitch-red);">Spam</span>
                                </div>
                                <div class="sample-email-content">
                                    <p>Subject: URGENT: Your Account Will Be Suspended</p>
                                    <p>Dear Valued Customer,</p>
                                    <p>We have detected suspicious activity on your account. Your account will be suspended within 24 hours unless you verify your information immediately. Click the link below to confirm your details and prevent account suspension.</p>
                                    <p>www.suspicious-link.com/verify</p>
                                    <p>Thank you for your prompt attention to this matter.</p>
                                    <p>Security Team</p>
                                </div>
                                <button class="btn btn-outline btn-sm use-sample" data-sample="spam">
                                    <i class="fas fa-copy"></i> Use This Example
                                </button>
                            </div>
                            
                            <div class="sample-email-card cyber-border" data-category="HR">
                                <div class="sample-email-header">
                                    <h3>HR Example</h3>
                                    <span class="sample-category" style="color: var(--neon-yellow);">HR</span>
                                </div>
                                <div class="sample-email-content">
                                    <p>Subject: Quarterly Performance Review - Action Required</p>
                                    <p>Hello Team,</p>
                                    <p>This is a reminder that quarterly performance reviews are due by the end of this week. Please complete your self-assessment in the HR portal and schedule a meeting with your manager to discuss your performance and goals for the next quarter.</p>
                                    <p>The HR team has prepared resources to help you with this process. You can find them in the shared drive under "Performance Review Resources".</p>
                                    <p>Best regards,</p>
                                    <p>HR Department</p>
                                </div>
                                <button class="btn btn-outline btn-sm use-sample" data-sample="hr">
                                    <i class="fas fa-copy"></i> Use This Example
                                </button>
                            </div>
                            
                            <div class="sample-email-card cyber-border" data-category="Support">
                                <div class="sample-email-header">
                                    <h3>Support Example</h3>
                                    <span class="sample-category" style="color: var(--neon-green);">Support</span>
                                </div>
                                <div class="sample-email-content">
                                    <p>Subject: Re: Ticket #45678 - Login Issue</p>
                                    <p>Hi John,</p>
                                    <p>Thank you for contacting our support team about your login issue. I've investigated the problem and found that your account was temporarily locked due to multiple failed login attempts.</p>
                                    <p>I've reset your account security and you should now be able to log in. If you continue to experience issues, please try clearing your browser cache and cookies, or use a different browser.</p>
                                    <p>Let me know if this resolves your issue or if you need further assistance.</p>
                                    <p>Best regards,</p>
                                    <p>Support Team</p>
                                </div>
                                <button class="btn btn-outline btn-sm use-sample" data-sample="support">
                                    <i class="fas fa-copy"></i> Use This Example
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Loading Overlay -->
    <div class="loading-container" style="display: none;">
        <div class="cyber-loading">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
        </div>
        <p class="loading-text neon-pulse">ANALYZING...</p>
    </div>
    
    <!-- Scripts -->
    <script src="../static/js/script.js"></script>
    <script>
        // Additional script for the classify page
        document.addEventListener('DOMContentLoaded', () => {
            // Sample email buttons
            const sampleButtons = document.querySelectorAll('.use-sample');
            const emailTextarea = document.querySelector('#email-text');
            
            const sampleEmails = {
                spam: document.querySelector('[data-category="Spam"] .sample-email-content').textContent,
                hr: document.querySelector('[data-category="HR"] .sample-email-content').textContent,
                support: document.querySelector('[data-category="Support"] .sample-email-content').textContent
            };
            
            sampleButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const sampleType = button.getAttribute('data-sample');
                    emailTextarea.value = sampleEmails[sampleType];
                    
                    // Scroll to form
                    document.querySelector('#classify-form').scrollIntoView({ behavior: 'smooth' });
                });
            });
        });
    </script>
</body>
</html>
