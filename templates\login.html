<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Live Email Classifier</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/animations.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/hacker.css') }}">
</head>
<body class="cyberpunk">
    <div class="matrix-background"></div>

    <div class="container">
        <header class="header">
            <h1 class="glitch" data-text="ACCESS PORTAL">ACCESS PORTAL</h1>
            <p class="subtitle">Authentication Required</p>
        </header>

        <main class="main">
            <div class="card">
                <div class="card-content">
                    <h2 class="card-title">Choose Authentication Method</h2>

                    {% if error %}
                    <div class="error-message">
                        {{ error }}
                    </div>
                    {% endif %}

                    <div class="auth-options">
                        <div class="auth-option">
                            <h3>Connect with Gmail</h3>
                            <p>Securely connect to your Gmail account using OAuth 2.0</p>
                            <p class="note">Your credentials are never stored on our servers</p>
                            <a href="#" class="btn btn-primary disabled">Connect Gmail (Coming Soon)</a>
                        </div>

                        <div class="auth-divider">OR</div>

                        <div class="auth-option">
                            <h3>Demo Mode</h3>
                            <p>Try the application with sample data</p>
                            <p class="note">No authentication required</p>
                            <a href="{{ url_for('auth.demo_auth') }}" class="btn btn-secondary">Enter Demo Mode</a>
                        </div>
                    </div>

                    <div class="back-link">
                        <a href="{{ url_for('main.index') }}">← Back to Home</a>
                    </div>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2023 Live Email Classifier - Cyberpunk Edition</p>
        </footer>
    </div>

    <script src="{{ url_for('static', filename='js/matrix.js') }}"></script>
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
</body>
</html>
