"""
Training Module for Live Email Classifier.
This module handles training and evaluation of the email classifier model.
"""

import pandas as pd
import numpy as np
import logging
import os
from typing import Dict, List, Any, Tuple, Optional
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, accuracy_score, confusion_matrix

from .preprocessor import EmailPreprocessor
from .model import EmailClassifier

logger = logging.getLogger(__name__)

def train_model(training_data_path: str, model_path: str, vectorizer_path: str, 
                categories: List[str] = None, test_size: float = 0.2) -> Dict[str, Any]:
    """
    Train the email classifier model.
    
    Args:
        training_data_path: Path to the training data CSV file
        model_path: Path to save the trained model
        vectorizer_path: Path to save the vectorizer
        categories: List of categories for classification (default: None)
        test_size: Proportion of data to use for testing (default: 0.2)
        
    Returns:
        Dict[str, Any]: Training results including accuracy and model info
    """
    try:
        # Load training data
        if not os.path.exists(training_data_path):
            logger.error(f"Training data file not found: {training_data_path}")
            return {"success": False, "error": "Training data file not found"}
        
        data = pd.read_csv(training_data_path)
        
        # Check if required columns exist
        if 'text' not in data.columns or 'category' not in data.columns:
            logger.error("Training data must contain 'text' and 'category' columns")
            return {"success": False, "error": "Invalid training data format"}
        
        # Get categories if not provided
        if not categories:
            categories = data['category'].unique().tolist()
        
        # Preprocess the text
        preprocessor = EmailPreprocessor()
        data['processed_text'] = data['text'].apply(preprocessor.preprocess_text)
        
        # Split data into training and testing sets
        X_train, X_test, y_train, y_test = train_test_split(
            data['processed_text'], data['category'], test_size=test_size, random_state=42
        )
        
        # Create and fit the vectorizer
        vectorizer = TfidfVectorizer(max_features=5000)
        X_train_vectorized = vectorizer.fit_transform(X_train)
        
        # Create and train the model
        model = MultinomialNB()
        model.fit(X_train_vectorized, y_train)
        
        # Create classifier instance and save model
        classifier = EmailClassifier(model_path, vectorizer_path, categories)
        classifier.model = model
        classifier.vectorizer = vectorizer
        classifier.save_model()
        
        # Evaluate the model
        X_test_vectorized = vectorizer.transform(X_test)
        y_pred = model.predict(X_test_vectorized)
        accuracy = accuracy_score(y_test, y_pred)
        
        logger.info(f"Model trained successfully with accuracy: {accuracy:.4f}")
        
        return {
            "success": True,
            "accuracy": accuracy,
            "categories": categories,
            "model_path": model_path,
            "vectorizer_path": vectorizer_path,
            "training_samples": len(X_train),
            "testing_samples": len(X_test)
        }
    
    except Exception as e:
        logger.error(f"Error during model training: {str(e)}")
        return {"success": False, "error": str(e)}

def evaluate_model(model_path: str, vectorizer_path: str, test_data_path: str = None, 
                  test_data: pd.DataFrame = None) -> Dict[str, Any]:
    """
    Evaluate the email classifier model.
    
    Args:
        model_path: Path to the trained model
        vectorizer_path: Path to the vectorizer
        test_data_path: Path to the test data CSV file (default: None)
        test_data: DataFrame containing test data (default: None)
        
    Returns:
        Dict[str, Any]: Evaluation results including accuracy and metrics
    """
    try:
        # Load the classifier
        classifier = EmailClassifier(model_path, vectorizer_path)
        if not classifier.load_model():
            return {"success": False, "error": "Failed to load model"}
        
        # Load test data if path is provided
        if test_data_path and not test_data:
            if not os.path.exists(test_data_path):
                logger.error(f"Test data file not found: {test_data_path}")
                return {"success": False, "error": "Test data file not found"}
            
            test_data = pd.read_csv(test_data_path)
        
        # Check if test data is provided
        if test_data is None:
            logger.error("No test data provided")
            return {"success": False, "error": "No test data provided"}
        
        # Check if required columns exist
        if 'text' not in test_data.columns or 'category' not in test_data.columns:
            logger.error("Test data must contain 'text' and 'category' columns")
            return {"success": False, "error": "Invalid test data format"}
        
        # Preprocess the text
        preprocessor = EmailPreprocessor()
        test_data['processed_text'] = test_data['text'].apply(preprocessor.preprocess_text)
        
        # Vectorize the text
        X_test_vectorized = classifier.vectorizer.transform(test_data['processed_text'])
        y_test = test_data['category']
        
        # Make predictions
        y_pred = classifier.model.predict(X_test_vectorized)
        
        # Calculate metrics
        accuracy = accuracy_score(y_test, y_pred)
        report = classification_report(y_test, y_pred, output_dict=True)
        conf_matrix = confusion_matrix(y_test, y_pred)
        
        logger.info(f"Model evaluation completed with accuracy: {accuracy:.4f}")
        
        return {
            "success": True,
            "accuracy": accuracy,
            "classification_report": report,
            "confusion_matrix": conf_matrix.tolist(),
            "test_samples": len(test_data)
        }
    
    except Exception as e:
        logger.error(f"Error during model evaluation: {str(e)}")
        return {"success": False, "error": str(e)}
