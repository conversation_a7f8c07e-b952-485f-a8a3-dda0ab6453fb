/* Hacker Theme for Live Email Classifier
   Dark, matrix-inspired theme with green terminal aesthetics */

:root {
  /* Hacker Color Palette */
  --hacker-green: #00ff00;
  --hacker-dark-green: #008800;
  --hacker-light-green: #88ff88;
  --hacker-black: #000000;
  --hacker-dark: #0a0a0a;
  --hacker-gray: #1a1a1a;
  --hacker-light-gray: #333333;
  --hacker-red: #ff0000;
  --hacker-blue: #0066ff;
  --hacker-yellow: #ffcc00;

  /* Typography */
  --font-main: 'Share Tech Mono', monospace;
  --font-alt: 'Share Tech Mono', monospace;
  --font-mono: 'Share Tech Mono', monospace;

  /* Effects */
  --glow-green: 0 0 10px var(--hacker-green), 0 0 20px rgba(0, 255, 0, 0.5);
  --glow-red: 0 0 10px var(--hacker-red), 0 0 20px rgba(255, 0, 0, 0.5);
  --glow-blue: 0 0 10px var(--hacker-blue), 0 0 20px rgba(0, 102, 255, 0.5);

  /* Override cyberpunk colors */
  --neon-blue: var(--hacker-green);
  --neon-pink: var(--hacker-red);
  --neon-yellow: var(--hacker-yellow);
  --neon-green: var(--hacker-light-green);
  --dark-blue: var(--hacker-dark);
  --dark-purple: var(--hacker-dark);
  --cyber-black: var(--hacker-black);
  --cyber-dark: var(--hacker-dark);
  --cyber-gray: var(--hacker-gray);
  --cyber-light: var(--hacker-light-gray);
  --glitch-blue: var(--hacker-green);
  --glitch-red: var(--hacker-red);
}

/* Base Styles */
body {
  font-family: var(--font-main);
  background-color: var(--hacker-black);
  color: var(--hacker-green);
  line-height: 1.6;
  overflow-x: hidden;
  background-image:
    linear-gradient(to bottom, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.98));
  background-size: cover;
  background-attachment: fixed;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--hacker-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--hacker-green);
  border-radius: 0;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--hacker-light-green);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-alt);
  color: var(--hacker-green);
  text-transform: uppercase;
  letter-spacing: 2px;
}

a {
  color: var(--hacker-green);
  text-decoration: none;
  position: relative;
  transition: all 0.3s ease;
}

a:hover {
  color: var(--hacker-light-green);
  text-shadow: var(--glow-green);
}

a::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 1px;
  bottom: -2px;
  left: 0;
  background: var(--hacker-green);
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s ease;
}

a:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

/* Header */
.header {
  border-bottom: 1px solid var(--hacker-green);
  background-color: rgba(0, 0, 0, 0.9);
}

.logo-text {
  font-family: var(--font-alt);
  color: var(--hacker-green);
  text-shadow: var(--glow-green);
}

/* Sidebar */
.sidebar {
  background-color: rgba(0, 0, 0, 0.9);
  border-right: 1px solid var(--hacker-green);
}

.nav-link {
  color: var(--hacker-green);
  border-left: 3px solid transparent;
}

.nav-link:hover, .nav-link.active {
  color: var(--hacker-light-green);
  border-left: 3px solid var(--hacker-green);
  background-color: rgba(0, 255, 0, 0.1);
}

/* Cards */
.card {
  background-color: rgba(0, 0, 0, 0.7);
  border: 1px solid var(--hacker-green);
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.2);
}

.card-header {
  border-bottom: 1px solid var(--hacker-green);
  background-color: rgba(0, 255, 0, 0.1);
}

/* Buttons */
.btn {
  background-color: transparent;
  color: var(--hacker-green);
  border: 1px solid var(--hacker-green);
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.btn:hover {
  background-color: var(--hacker-green);
  color: var(--hacker-black);
  box-shadow: var(--glow-green);
}

.btn-primary {
  background-color: var(--hacker-green);
  color: var(--hacker-black);
  border: 1px solid var(--hacker-green);
}

.btn-primary:hover {
  background-color: var(--hacker-light-green);
  border: 1px solid var(--hacker-light-green);
}

.btn-secondary {
  background-color: var(--hacker-red);
  color: var(--hacker-black);
  border: 1px solid var(--hacker-red);
}

.btn-secondary:hover {
  background-color: #ff3333;
  border: 1px solid #ff3333;
}

/* Forms */
input, textarea, select {
  background-color: var(--hacker-dark);
  color: var(--hacker-green);
  border: 1px solid var(--hacker-green);
  font-family: var(--font-mono);
}

input:focus, textarea:focus, select:focus {
  border-color: var(--hacker-light-green);
  box-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
}

/* Terminal effect for text areas */
textarea {
  background-color: var(--hacker-black);
  color: var(--hacker-green);
  font-family: var(--font-mono);
  border: 1px solid var(--hacker-green);
  padding: 10px;
  resize: none;
  caret-color: var(--hacker-green);
}

/* Matrix rain effect */
.matrix-rain {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

/* Override scanline effect */
.scanline {
  background: linear-gradient(to bottom, transparent, rgba(0, 255, 0, 0.04), transparent);
}

/* Override neon pulse */
.neon-pulse {
  animation: hacker-pulse 1.5s ease-in-out infinite alternate;
}

@keyframes hacker-pulse {
  from {
    text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px var(--hacker-green), 0 0 20px var(--hacker-green), 0 0 25px var(--hacker-green);
  }
  to {
    text-shadow: 0 0 10px #fff, 0 0 20px var(--hacker-green), 0 0 30px var(--hacker-green), 0 0 40px var(--hacker-green);
  }
}

/* Override cyber border */
.cyber-border::before {
  border: 1px solid var(--hacker-green);
}

@keyframes cyber-border-animation {
  0%, 100% {
    clip-path: inset(0 0 98% 0);
    border-color: var(--hacker-green);
  }
  25% {
    clip-path: inset(0 98% 0 0);
    border-color: var(--hacker-green);
  }
  50% {
    clip-path: inset(98% 0 0 0);
    border-color: var(--hacker-green);
  }
  75% {
    clip-path: inset(0 0 0 98%);
    border-color: var(--hacker-green);
  }
}

/* Loading animation */
.cyber-loading div {
  background: var(--hacker-green);
}

.cyber-loading div:nth-child(1),
.cyber-loading div:nth-child(2),
.cyber-loading div:nth-child(3),
.cyber-loading div:nth-child(4) {
  background: var(--hacker-green);
}

/* Terminal cursor blink */
.terminal-cursor {
  display: inline-block;
  width: 10px;
  height: 20px;
  background-color: var(--hacker-green);
  animation: cursor-blink 1s infinite;
}

@keyframes cursor-blink {
  0%, 49% {
    opacity: 1;
  }
  50%, 100% {
    opacity: 0;
  }
}

/* Notice styles */
.demo-notice {
  padding: 15px;
  border: 1px solid var(--hacker-yellow);
  background-color: rgba(255, 204, 0, 0.1);
  margin-bottom: 20px;
  border-radius: 4px;
}

.demo-notice h3 {
  color: var(--hacker-yellow);
  margin-top: 0;
}

.demo-notice a {
  color: var(--hacker-yellow);
  text-decoration: underline;
}

.demo-notice a:hover {
  color: var(--hacker-light-green);
}

.error-notice {
  padding: 15px;
  border: 1px solid var(--hacker-red);
  background-color: rgba(255, 0, 0, 0.1);
  margin-bottom: 20px;
  border-radius: 4px;
}

.error-notice h3 {
  color: var(--hacker-red);
  margin-top: 0;
}

.success-notice {
  padding: 15px;
  border: 1px solid var(--hacker-green);
  background-color: rgba(0, 255, 0, 0.1);
  margin-bottom: 20px;
  border-radius: 4px;
}

.success-notice h3 {
  color: var(--hacker-green);
  margin-top: 0;
}

.info-notice {
  padding: 15px;
  border: 1px solid var(--hacker-blue);
  background-color: rgba(0, 102, 255, 0.1);
  margin-bottom: 20px;
  border-radius: 4px;
}

.info-notice h3 {
  color: var(--hacker-blue);
  margin-top: 0;
}

.info-notice .oauth-info {
  margin-top: 15px;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  border-left: 3px solid var(--hacker-green);
}

.info-notice .inline-link {
  color: var(--hacker-green);
  text-decoration: underline;
  font-weight: bold;
}

.info-notice .inline-link:hover {
  color: var(--hacker-light-green);
}

.flash-message {
  margin-bottom: 20px;
}
