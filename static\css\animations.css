/* Cyberpunk Animations for Live Email Classifier
   High-impact animations and effects */

/* Glitch Effect */
.glitch {
  position: relative;
  color: white;
  font-size: 4em;
  letter-spacing: 0.5em;
  animation: glitch-skew 1s infinite linear alternate-reverse;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  left: 2px;
  text-shadow: -2px 0 var(--glitch-red);
  clip: rect(44px, 450px, 56px, 0);
  animation: glitch-anim 5s infinite linear alternate-reverse;
}

.glitch::after {
  left: -2px;
  text-shadow: -2px 0 var(--glitch-blue);
  clip: rect(44px, 450px, 56px, 0);
  animation: glitch-anim2 5s infinite linear alternate-reverse;
}

@keyframes glitch-anim {
  0% {
    clip: rect(31px, 9999px, 94px, 0);
    transform: skew(0.85deg);
  }
  5% {
    clip: rect(70px, 9999px, 71px, 0);
    transform: skew(0.17deg);
  }
  10% {
    clip: rect(75px, 9999px, 92px, 0);
    transform: skew(0.4deg);
  }
  15% {
    clip: rect(12px, 9999px, 23px, 0);
    transform: skew(0.29deg);
  }
  20% {
    clip: rect(18px, 9999px, 93px, 0);
    transform: skew(0.67deg);
  }
  25% {
    clip: rect(20px, 9999px, 31px, 0);
    transform: skew(0.84deg);
  }
  30% {
    clip: rect(13px, 9999px, 1px, 0);
    transform: skew(0.7deg);
  }
  35% {
    clip: rect(76px, 9999px, 72px, 0);
    transform: skew(0.2deg);
  }
  40% {
    clip: rect(89px, 9999px, 14px, 0);
    transform: skew(0.67deg);
  }
  45% {
    clip: rect(19px, 9999px, 10px, 0);
    transform: skew(0.98deg);
  }
  50% {
    clip: rect(86px, 9999px, 52px, 0);
    transform: skew(0.28deg);
  }
  55% {
    clip: rect(33px, 9999px, 27px, 0);
    transform: skew(0.39deg);
  }
  60% {
    clip: rect(68px, 9999px, 68px, 0);
    transform: skew(0.77deg);
  }
  65% {
    clip: rect(68px, 9999px, 100px, 0);
    transform: skew(0.05deg);
  }
  70% {
    clip: rect(100px, 9999px, 82px, 0);
    transform: skew(0.24deg);
  }
  75% {
    clip: rect(97px, 9999px, 72px, 0);
    transform: skew(0.06deg);
  }
  80% {
    clip: rect(38px, 9999px, 27px, 0);
    transform: skew(0.85deg);
  }
  85% {
    clip: rect(48px, 9999px, 90px, 0);
    transform: skew(0.26deg);
  }
  90% {
    clip: rect(75px, 9999px, 15px, 0);
    transform: skew(0.46deg);
  }
  95% {
    clip: rect(67px, 9999px, 68px, 0);
    transform: skew(0.05deg);
  }
  100% {
    clip: rect(19px, 9999px, 84px, 0);
    transform: skew(0.48deg);
  }
}

@keyframes glitch-anim2 {
  0% {
    clip: rect(65px, 9999px, 119px, 0);
    transform: skew(0.02deg);
  }
  5% {
    clip: rect(110px, 9999px, 74px, 0);
    transform: skew(0.05deg);
  }
  10% {
    clip: rect(131px, 9999px, 74px, 0);
    transform: skew(0.99deg);
  }
  15% {
    clip: rect(144px, 9999px, 144px, 0);
    transform: skew(0.4deg);
  }
  20% {
    clip: rect(61px, 9999px, 7px, 0);
    transform: skew(0.28deg);
  }
  25% {
    clip: rect(138px, 9999px, 115px, 0);
    transform: skew(0.39deg);
  }
  30% {
    clip: rect(131px, 9999px, 107px, 0);
    transform: skew(0.02deg);
  }
  35% {
    clip: rect(39px, 9999px, 31px, 0);
    transform: skew(0.6deg);
  }
  40% {
    clip: rect(64px, 9999px, 15px, 0);
    transform: skew(0.55deg);
  }
  45% {
    clip: rect(149px, 9999px, 121px, 0);
    transform: skew(0.93deg);
  }
  50% {
    clip: rect(147px, 9999px, 69px, 0);
    transform: skew(0.12deg);
  }
  55% {
    clip: rect(36px, 9999px, 75px, 0);
    transform: skew(0.56deg);
  }
  60% {
    clip: rect(46px, 9999px, 144px, 0);
    transform: skew(0.99deg);
  }
  65% {
    clip: rect(131px, 9999px, 37px, 0);
    transform: skew(0.4deg);
  }
  70% {
    clip: rect(117px, 9999px, 91px, 0);
    transform: skew(0.79deg);
  }
  75% {
    clip: rect(106px, 9999px, 140px, 0);
    transform: skew(0.73deg);
  }
  80% {
    clip: rect(69px, 9999px, 27px, 0);
    transform: skew(0.7deg);
  }
  85% {
    clip: rect(101px, 9999px, 52px, 0);
    transform: skew(0.01deg);
  }
  90% {
    clip: rect(18px, 9999px, 149px, 0);
    transform: skew(0.12deg);
  }
  95% {
    clip: rect(53px, 9999px, 142px, 0);
    transform: skew(0.58deg);
  }
  100% {
    clip: rect(132px, 9999px, 74px, 0);
    transform: skew(0.61deg);
  }
}

@keyframes glitch-skew {
  0% {
    transform: skew(-2deg);
  }
  10% {
    transform: skew(1deg);
  }
  20% {
    transform: skew(0deg);
  }
  30% {
    transform: skew(1deg);
  }
  40% {
    transform: skew(-1deg);
  }
  50% {
    transform: skew(2deg);
  }
  60% {
    transform: skew(0deg);
  }
  70% {
    transform: skew(2deg);
  }
  80% {
    transform: skew(-2deg);
  }
  90% {
    transform: skew(1deg);
  }
  100% {
    transform: skew(0deg);
  }
}

/* Neon Pulse */
.neon-pulse {
  animation: neon-pulse 1.5s ease-in-out infinite alternate;
}

@keyframes neon-pulse {
  from {
    text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px var(--neon-blue), 0 0 20px var(--neon-blue), 0 0 25px var(--neon-blue), 0 0 30px var(--neon-blue), 0 0 35px var(--neon-blue);
  }
  to {
    text-shadow: 0 0 10px #fff, 0 0 20px #fff, 0 0 30px var(--neon-pink), 0 0 40px var(--neon-pink), 0 0 50px var(--neon-pink), 0 0 60px var(--neon-pink), 0 0 70px var(--neon-pink);
  }
}

/* Scan Line Effect */
.scanline {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, transparent, rgba(0, 243, 255, 0.04), transparent);
  pointer-events: none;
  z-index: 9999;
  animation: scanline 8s linear infinite;
}

@keyframes scanline {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100vh);
  }
}

/* CRT Flicker */
.crt-flicker {
  animation: flicker 0.15s infinite;
}

@keyframes flicker {
  0% {
    opacity: 0.9;
  }
  5% {
    opacity: 0.8;
  }
  10% {
    opacity: 0.9;
  }
  15% {
    opacity: 0.85;
  }
  20% {
    opacity: 0.9;
  }
  50% {
    opacity: 1;
  }
  80% {
    opacity: 0.9;
  }
  90% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.9;
  }
}

/* Cyber Border */
.cyber-border {
  position: relative;
}

.cyber-border::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 1px solid var(--neon-blue);
  animation: cyber-border-animation 2s linear infinite;
  z-index: -1;
}

@keyframes cyber-border-animation {
  0% {
    clip-path: inset(0 0 98% 0);
    border-color: var(--neon-blue);
  }
  25% {
    clip-path: inset(0 98% 0 0);
    border-color: var(--neon-pink);
  }
  50% {
    clip-path: inset(98% 0 0 0);
    border-color: var(--neon-yellow);
  }
  75% {
    clip-path: inset(0 0 0 98%);
    border-color: var(--neon-green);
  }
  100% {
    clip-path: inset(0 0 98% 0);
    border-color: var(--neon-blue);
  }
}

/* Loading Animation */
.cyber-loading {
  display: inline-block;
  position: relative;
  width: 80px;
  height: 80px;
}

.cyber-loading div {
  position: absolute;
  top: 33px;
  width: 13px;
  height: 13px;
  border-radius: 50%;
  background: var(--neon-blue);
  animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.cyber-loading div:nth-child(1) {
  left: 8px;
  animation: cyber-loading1 0.6s infinite;
  background: var(--neon-blue);
}

.cyber-loading div:nth-child(2) {
  left: 8px;
  animation: cyber-loading2 0.6s infinite;
  background: var(--neon-pink);
}

.cyber-loading div:nth-child(3) {
  left: 32px;
  animation: cyber-loading2 0.6s infinite;
  background: var(--neon-yellow);
}

.cyber-loading div:nth-child(4) {
  left: 56px;
  animation: cyber-loading3 0.6s infinite;
  background: var(--neon-green);
}

@keyframes cyber-loading1 {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes cyber-loading3 {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0);
  }
}

@keyframes cyber-loading2 {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(24px, 0);
  }
}
