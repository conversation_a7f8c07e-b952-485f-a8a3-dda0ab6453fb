<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Email Classifier - Hacker Edition</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/animations.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/hacker.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/visualization.css') }}">
</head>
<body>
    <!-- Scanline Effect -->
    <div class="scanline"></div>

    <!-- Header -->
    <header class="header">
        <div class="logo">
            <span class="logo-text neon-pulse">LIVE EMAIL CLASSIFIER</span>
        </div>

        <div class="header-actions">
            <button id="theme-toggle" class="btn btn-outline">
                <i class="fas fa-moon"></i> Toggle Theme
            </button>
        </div>
    </header>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="user-info" {% if user_info %}data-authenticated="true" data-user-info='{{ user_info|tojson }}'{% else %}data-authenticated="false"{% endif %}>
                <div class="user-avatar cyber-border">
                    <img src="https://via.placeholder.com/100x100?text=HACKER" alt="User Avatar">
                </div>
                <h3 class="user-name terminal-text">{% if user_info %}{{ user_info.email }}{% else %}ANONYMOUS{% endif %}</h3>
                <p class="user-role">Email Security Analyst</p>

                <div class="user-actions">
                    <a href="{{ url_for('auth.auth_options') }}" class="btn btn-primary unauthenticated-only" style="{% if user_info %}display: none;{% endif %}">
                        <i class="fas fa-sign-in-alt"></i> Connect Gmail
                    </a>
                    <a href="{{ url_for('auth.logout') }}" class="btn btn-outline authenticated-only" style="{% if not user_info %}display: none;{% endif %}">
                        <i class="fas fa-sign-out-alt"></i> Disconnect
                    </a>
                </div>
            </div>

            <nav class="nav-menu">
                <ul>
                    <li class="nav-item">
                        <a href="#" class="nav-link active">
                            <i class="fas fa-home"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-envelope"></i> Emails
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-filter"></i> Classify
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-cog"></i> Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-info-circle"></i> About
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Content Area -->
        <div class="content">
            <h1 class="terminal-text">Email Classification System</h1>
            <p class="terminal-text">Welcome to the secure email classification interface. This system automatically categorizes incoming emails using advanced machine learning algorithms.</p>

            {% if flash_message %}
            <div class="flash-message card">
                {{ flash_message|safe }}
            </div>
            {% endif %}

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="card stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Total Emails</h3>
                        <div class="stat-value counter" data-target="1247">0</div>
                    </div>
                </div>

                <div class="card stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Classified</h3>
                        <div class="stat-value counter" data-target="1182">0</div>
                    </div>
                </div>

                <div class="card stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Spam Detected</h3>
                        <div class="stat-value counter" data-target="523">0</div>
                    </div>
                </div>

                <div class="card stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Accuracy</h3>
                        <div class="stat-value counter" data-target="97">0</div>
                        <span>%</span>
                    </div>
                </div>
            </div>

            <!-- Email Classification -->
            <div class="card">
                <div class="card-header">
                    <h2>Analyze Email</h2>
                </div>
                <div class="card-body">
                    <form id="analyze-form">
                        <div class="form-group">
                            <label for="email-subject">Email Subject (optional):</label>
                            <input type="text" id="email-subject" name="email-subject" placeholder="Enter email subject...">
                        </div>
                        <div class="form-group">
                            <label for="email-text">Email Content:</label>
                            <textarea id="email-text" name="email-text" rows="6" placeholder="Paste email content here..."></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter"></i> Analyze
                            </button>
                            <button type="button" class="btn btn-outline use-sample" data-sample="spam">
                                <i class="fas fa-bug"></i> Sample Spam
                            </button>
                            <button type="button" class="btn btn-outline use-sample" data-sample="hr">
                                <i class="fas fa-user-tie"></i> Sample Professional
                            </button>
                            <button type="button" class="btn btn-outline use-sample" data-sample="support">
                                <i class="fas fa-headset"></i> Sample Casual
                            </button>
                        </div>
                    </form>

                    <div id="analysis-result" style="display: none;"></div>

                    <!-- Email Classification Visualization -->
                    <div class="visualization-section">
                        <h3 class="visualization-title">Classification Visualization</h3>
                        <div id="visualization-container" class="visualization-container">
                            <!-- Visualization will be rendered here -->
                        </div>
                        <div class="visualization-controls">
                            <button id="reset-visualization" class="btn btn-outline">
                                <i class="fas fa-redo"></i> Reset
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gmail Integration -->
            <div class="card authenticated-only" style="{% if not user_info %}display: none;{% endif %}">
                <div class="card-header">
                    <h2>Gmail Integration</h2>
                </div>
                <div class="card-body">
                    <div class="gmail-stats">
                        {% if user_info %}
                        <div class="stat-item">
                            <span class="stat-label">Total Messages:</span>
                            <span class="stat-value">{{ user_info.messages_total }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Total Threads:</span>
                            <span class="stat-value">{{ user_info.threads_total }}</span>
                        </div>
                        {% endif %}
                    </div>

                    <div class="gmail-actions">
                        <button id="fetch-emails" class="btn btn-primary">
                            <i class="fas fa-sync"></i> Fetch & Analyze Emails
                        </button>
                    </div>
                </div>
            </div>

            <!-- Recent Emails -->
            <div class="card">
                <div class="card-header">
                    <h2>Recent Emails</h2>
                </div>
                <div class="card-body">
                    <div class="emails-container">
                        <!-- Email items will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-container" style="display: none;">
        <div class="cyber-loading">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
        </div>
        <p class="loading-text neon-pulse">PROCESSING...</p>
    </div>

    <!-- Sample Email Templates (hidden) -->
    <div style="display: none;">
        <div class="sample-email" data-category="Spam">
            <div class="sample-email-content">
                Subject: URGENT: Your Account Has Been Compromised

                Dear Valued Customer,

                We have detected suspicious activity on your account. Your account access will be terminated unless you verify your information immediately. Click the link below to confirm your identity and restore access.

                [SUSPICIOUS LINK REMOVED]

                This is an urgent matter that requires your immediate attention.

                Security Team
            </div>
        </div>

        <div class="sample-email" data-category="HR">
            <div class="sample-email-content">
                Subject: Quarterly Performance Review Scheduled

                Hello Team Member,

                This is a reminder that your quarterly performance review has been scheduled for next week. Please prepare your self-assessment and project highlights for the past quarter.

                The meeting will take place on Tuesday at 2:00 PM in the conference room. If you need to reschedule, please contact HR as soon as possible.

                Best regards,
                Human Resources Department
            </div>
        </div>

        <div class="sample-email" data-category="Support">
            <div class="sample-email-content">
                Subject: Re: Ticket #45678 - Login Issue

                Hello,

                Thank you for contacting technical support regarding your login issue. Based on our investigation, it appears that your account was temporarily locked due to multiple failed login attempts.

                We have reset your account status. Please try logging in again with your current credentials. If you continue to experience issues, please let us know.

                Technical Support Team
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    <script src="{{ url_for('static', filename='js/matrix.js') }}"></script>
    <script src="{{ url_for('static', filename='js/gmail-integration.js') }}"></script>
    <script src="{{ url_for('static', filename='js/visualization.js') }}"></script>
</body>
</html>
