<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emails - Live Email Classifier</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/animations.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/hacker.css') }}">
</head>
<body class="cyberpunk">
    <div class="matrix-background"></div>

    <div class="container dashboard-container">
        <header class="header dashboard-header">
            <div class="logo">
                <h1 class="glitch" data-text="EMAILS">EMAILS</h1>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="{{ url_for('main.index') }}">Dashboard</a></li>
                    <li><a href="{{ url_for('main.emails') }}" class="active">Emails</a></li>
                    <li><a href="{{ url_for('main.classify') }}">Classify</a></li>
                </ul>
            </nav>
            <div class="user-info">
                <span class="user-email">{{ session.get('user_info', {}).get('email', '<EMAIL>') }}</span>
                <a href="{{ url_for('logout') }}" class="btn btn-small">Logout</a>
            </div>
        </header>

        <main class="main dashboard-main">
            <div class="emails-container">
                <div class="card emails-card">
                    <div class="card-content">
                        <div class="card-header">
                            <h2 class="card-title">Classified Emails</h2>
                            <div class="card-actions">
                                <div class="filter-dropdown">
                                    <button class="btn btn-small">Filter ▼</button>
                                    <div class="dropdown-content">
                                        <a href="#" data-filter="all">All</a>
                                        <a href="#" data-filter="spam">Spam</a>
                                        <a href="#" data-filter="professional">Professional</a>
                                        <a href="#" data-filter="casual">Casual</a>
                                    </div>
                                </div>
                                <button class="btn btn-small refresh-btn">Refresh</button>
                            </div>
                        </div>

                        <div class="emails-list">
                            {% for email in emails %}
                            <div class="email-item {{ email.category }}">
                                <div class="email-header">
                                    <div class="email-meta">
                                        <span class="email-date">{{ email.date }}</span>
                                        <span class="email-category {{ email.category }}">{{ email.category|upper }}</span>
                                        <span class="email-confidence">{{ (email.confidence * 100)|int }}%</span>
                                    </div>
                                    <h3 class="email-subject">{{ email.subject }}</h3>
                                    <div class="email-sender">From: {{ email.sender }}</div>
                                </div>
                                <div class="email-snippet">{{ email.snippet }}</div>
                                <div class="email-actions">
                                    <button class="btn btn-small view-btn" data-id="{{ email.id }}">View</button>
                                    <button class="btn btn-small analyze-btn" data-id="{{ email.id }}">Re-analyze</button>
                                </div>
                                <div class="email-body" id="body-{{ email.id }}" style="display: none;">
                                    <pre>{{ email.body }}</pre>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        {% if not emails %}
                        <div class="no-emails">
                            <p>No emails found. This could be because:</p>
                            <ul>
                                <li>You haven't connected your Gmail account</li>
                                <li>Your Gmail account has no emails</li>
                                <li>The filter is too restrictive</li>
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div class="card demo-notice-card">
                    <div class="card-content">
                        <h2 class="card-title">Demo Mode Active</h2>
                        <p>You are currently in demo mode. All emails shown are sample data.</p>
                        <p>To view your real emails, connect your Gmail account.</p>
                    </div>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2023 Live Email Classifier - Cyberpunk Edition</p>
        </footer>
    </div>

    <script src="{{ url_for('static', filename='js/matrix.js') }}"></script>
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // View email functionality
            document.querySelectorAll('.view-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const emailId = this.getAttribute('data-id');
                    const bodyElement = document.getElementById('body-' + emailId);

                    if (bodyElement.style.display === 'none') {
                        bodyElement.style.display = 'block';
                        this.textContent = 'Hide';
                    } else {
                        bodyElement.style.display = 'none';
                        this.textContent = 'View';
                    }
                });
            });

            // Filter functionality
            document.querySelectorAll('.filter-dropdown a').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const filter = this.getAttribute('data-filter');

                    document.querySelectorAll('.email-item').forEach(item => {
                        if (filter === 'all' || item.classList.contains(filter)) {
                            item.style.display = 'block';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });
        });
    </script>
</body>
</html>
