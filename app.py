"""
Live Email Classifier - Main Application
A Flask web application that classifies emails into categories (Spam, Professional, Casual)
using machine learning with a cyberpunk-themed UI.
"""

import os
import sys
import logging
import secrets
import datetime

# Add project paths to sys.path for imports
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'config'))
sys.path.insert(0, os.path.join(project_root, 'app', 'services'))
sys.path.insert(0, os.path.join(project_root, 'classifier'))

# Standard library imports
from flask import (
    Flask,
    render_template,
    request,
    jsonify,
    redirect,
    url_for,
    session,
    flash
)

# Configuration related imports
try:
    from config.config import get_config, update_config, save_settings_to_json
except ImportError:
    # Fallback import
    sys.path.append('config')
    from config import get_config, update_config, save_settings_to_json

# Email processing and classification
from app.services.email_processor import EmailProcessor
from classifier.model import EmailClassifier
from classifier.preprocessor import EmailPreprocessor
from classifier.training import train_model
from app.services.email_analyzer import EmailAnalyzer

# Google authentication and API
from app.services.google_auth import (
    get_gmail_service,
    start_oauth_flow,
    complete_oauth_flow,
    fetch_emails,
    authenticate_with_credentials
)

# Database operations
from app.services.database import (
    get_user_by_email,
    create_user,
    update_user,
    save_email,
    get_emails,
    update_statistics,
    get_statistics
)

# Initialize Flask app
app = Flask(__name__,
            static_folder='static',
            template_folder='templates')

# Configure app
app.secret_key = secrets.token_hex(16)
app.config['SESSION_TYPE'] = 'filesystem'
app.config['OAUTH_REDIRECT_URI'] = 'http://localhost:8080/oauth2callback'

# Register blueprints
from app.routes.main import main_bp
from app.routes.auth import auth_bp
from app.routes.api import api_bp

app.register_blueprint(main_bp)
app.register_blueprint(auth_bp)
app.register_blueprint(api_bp)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('email_classifier.log')
    ]
)

logger = logging.getLogger(__name__)

# Load configuration
config = get_config()

# Initialize classifier and preprocessor
classifier = None
preprocessor = None

# Initialize email analyzer
email_analyzer = EmailAnalyzer()

# Gmail service
gmail_service = None
user_info = None

def initialize_classifier():
    """Initialize the email classifier."""
    global classifier, preprocessor

    try:
        # Initialize classifier
        classifier = EmailClassifier(
            model_path=config['classifier']['model_path'],
            vectorizer_path=config['classifier']['vectorizer_path'],
            categories=config['classifier']['categories']
        )

        # Train model if it doesn't exist
        if not os.path.exists(config['classifier']['model_path']):
            logger.info("Training new model...")

            # Create sample training data if it doesn't exist
            if not os.path.exists(config['classifier']['training_data']):
                from main import create_sample_training_data
                create_sample_training_data()

            train_result = train_model(
                training_data_path=config['classifier']['training_data'],
                model_path=config['classifier']['model_path'],
                vectorizer_path=config['classifier']['vectorizer_path'],
                categories=config['classifier']['categories']
            )

            if not train_result['success']:
                logger.error(f"Model training failed: {train_result.get('error', 'Unknown error')}")
                return False

        # Load the model
        if not classifier.load_model():
            logger.error("Failed to load model")
            return False

        # Initialize preprocessor
        preprocessor = EmailPreprocessor()

        logger.info("Classifier initialized successfully")
        return True

    except Exception as e:
        logger.error(f"Error initializing classifier: {str(e)}")
        return False

# Routes (main routes are now in blueprints)









# Initialize the classifier when the module is loaded
initialize_classifier()

if __name__ == '__main__':
    # Create data directory if it doesn't exist
    os.makedirs('data/model', exist_ok=True)

    # Run the Flask app
    app.run(debug=True, host='localhost', port=8080)


