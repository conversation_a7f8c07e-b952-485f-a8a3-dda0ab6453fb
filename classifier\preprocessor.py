"""
Email Preprocessor Module for Live Email Classifier.
This module handles preprocessing of email text for classification.
"""

import re
import string
import logging
from typing import List, Dict, Any, Optional
import nltk
from nltk.corpus import stopwords
from nltk.stem import WordNetLemmatizer
from nltk.tokenize import word_tokenize

logger = logging.getLogger(__name__)

# Download required NLTK resources
try:
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
    nltk.download('wordnet', quiet=True)
except Exception as e:
    logger.warning(f"Failed to download NLTK resources: {str(e)}")

class EmailPreprocessor:
    """Class for preprocessing email text for classification."""
    
    def __init__(self, remove_stopwords: bool = True, lemmatize: bool = True):
        """
        Initialize the EmailPreprocessor.
        
        Args:
            remove_stopwords: Whether to remove stopwords (default: True)
            lemmatize: Whether to lemmatize words (default: True)
        """
        self.remove_stopwords = remove_stopwords
        self.lemmatize = lemmatize
        
        # Initialize stopwords and lemmatizer
        try:
            self.stop_words = set(stopwords.words('english'))
        except:
            logger.warning("Failed to load stopwords, using empty set")
            self.stop_words = set()
        
        self.lemmatizer = WordNetLemmatizer() if lemmatize else None
    
    def preprocess(self, email_data: Dict[str, Any]) -> str:
        """
        Preprocess an email for classification.
        
        Args:
            email_data: Dictionary containing email data
            
        Returns:
            str: Preprocessed text ready for classification
        """
        # Extract text from email data
        text = ""
        if 'subject' in email_data and email_data['subject']:
            text += email_data['subject'] + " "
        
        if 'body' in email_data and email_data['body']:
            text += email_data['body']
        
        # Clean and preprocess the text
        return self.preprocess_text(text)
    
    def preprocess_text(self, text: str) -> str:
        """
        Preprocess text for classification.
        
        Args:
            text: Raw text to preprocess
            
        Returns:
            str: Preprocessed text
        """
        if not text:
            return ""
        
        try:
            # Convert to lowercase
            text = text.lower()
            
            # Remove email addresses
            text = re.sub(r'\S*@\S*\s?', '', text)
            
            # Remove URLs
            text = re.sub(r'http\S+', '', text)
            
            # Remove punctuation
            text = text.translate(str.maketrans('', '', string.punctuation))
            
            # Tokenize
            tokens = word_tokenize(text)
            
            # Remove stopwords if enabled
            if self.remove_stopwords:
                tokens = [token for token in tokens if token not in self.stop_words]
            
            # Lemmatize if enabled
            if self.lemmatize and self.lemmatizer:
                tokens = [self.lemmatizer.lemmatize(token) for token in tokens]
            
            # Join tokens back into a string
            preprocessed_text = ' '.join(tokens)
            
            return preprocessed_text
        
        except Exception as e:
            logger.error(f"Error during text preprocessing: {str(e)}")
            return text  # Return original text if preprocessing fails
    
    def extract_features(self, email_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract additional features from email data.
        
        Args:
            email_data: Dictionary containing email data
            
        Returns:
            Dict[str, Any]: Dictionary of extracted features
        """
        features = {}
        
        try:
            # Sender domain
            if 'from' in email_data and email_data['from']:
                sender = email_data['from']
                domain_match = re.search(r'@([\w.-]+)', sender)
                if domain_match:
                    features['sender_domain'] = domain_match.group(1)
            
            # Email length
            if 'body' in email_data and email_data['body']:
                features['email_length'] = len(email_data['body'])
            
            # Has attachments
            if 'has_attachments' in email_data:
                features['has_attachments'] = email_data['has_attachments']
            
            # Time of day
            if 'date' in email_data and email_data['date']:
                features['hour_of_day'] = email_data['date'].hour
            
        except Exception as e:
            logger.error(f"Error extracting features: {str(e)}")
        
        return features
