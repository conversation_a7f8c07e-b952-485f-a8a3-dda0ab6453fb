/**
 * Cyberpunk-themed Live Email Classifier
 * Main JavaScript functionality
 */

// DOM Elements
document.addEventListener('DOMContentLoaded', () => {
  // Initialize UI elements
  initializeUI();
  
  // Add event listeners
  addEventListeners();
  
  // Start animations
  startAnimations();
  
  // Load dashboard data if on dashboard page
  if (document.querySelector('.dashboard-container')) {
    loadDashboardData();
  }
  
  // Load email data if on emails page
  if (document.querySelector('.emails-container')) {
    loadEmails();
  }
});

/**
 * Initialize UI elements
 */
function initializeUI() {
  console.log('Initializing UI...');
  
  // Add scanline effect
  const scanline = document.createElement('div');
  scanline.className = 'scanline';
  document.body.appendChild(scanline);
  
  // Initialize glitch text
  const glitchElements = document.querySelectorAll('.glitch');
  glitchElements.forEach(el => {
    el.setAttribute('data-text', el.textContent);
  });
  
  // Initialize tooltips
  const tooltips = document.querySelectorAll('[data-tooltip]');
  tooltips.forEach(tooltip => {
    const tooltipText = tooltip.getAttribute('data-tooltip');
    const tooltipEl = document.createElement('div');
    tooltipEl.className = 'tooltip';
    tooltipEl.textContent = tooltipText;
    tooltip.appendChild(tooltipEl);
    
    tooltip.addEventListener('mouseenter', () => {
      tooltipEl.style.opacity = '1';
      tooltipEl.style.visibility = 'visible';
    });
    
    tooltip.addEventListener('mouseleave', () => {
      tooltipEl.style.opacity = '0';
      tooltipEl.style.visibility = 'hidden';
    });
  });
  
  // Initialize progress bars
  const progressBars = document.querySelectorAll('.progress-bar');
  progressBars.forEach(bar => {
    const value = bar.getAttribute('data-value');
    const progressInner = bar.querySelector('.progress-inner');
    progressInner.style.width = `${value}%`;
    
    // Add color based on value
    if (value < 30) {
      progressInner.style.backgroundColor = 'var(--glitch-red)';
    } else if (value < 70) {
      progressInner.style.backgroundColor = 'var(--neon-yellow)';
    } else {
      progressInner.style.backgroundColor = 'var(--neon-green)';
    }
  });
  
  // Initialize terminal
  const terminal = document.querySelector('.terminal');
  if (terminal) {
    initTerminal(terminal);
  }
}

/**
 * Add event listeners to interactive elements
 */
function addEventListeners() {
  // Mobile menu toggle
  const menuToggle = document.querySelector('.menu-toggle');
  const sidebar = document.querySelector('.sidebar');
  
  if (menuToggle && sidebar) {
    menuToggle.addEventListener('click', () => {
      sidebar.classList.toggle('active');
      menuToggle.classList.toggle('active');
    });
  }
  
  // Settings form submission
  const settingsForm = document.querySelector('#settings-form');
  if (settingsForm) {
    settingsForm.addEventListener('submit', (e) => {
      e.preventDefault();
      saveSettings(settingsForm);
    });
  }
  
  // Email classification form
  const classifyForm = document.querySelector('#classify-form');
  if (classifyForm) {
    classifyForm.addEventListener('submit', (e) => {
      e.preventDefault();
      classifyEmail(classifyForm);
    });
  }
  
  // Theme toggle
  const themeToggle = document.querySelector('#theme-toggle');
  if (themeToggle) {
    themeToggle.addEventListener('click', () => {
      document.body.classList.toggle('light-theme');
      const isDark = !document.body.classList.contains('light-theme');
      localStorage.setItem('darkTheme', isDark);
      
      // Update toggle text
      themeToggle.textContent = isDark ? 'Light Mode' : 'Dark Mode';
    });
  }
  
  // Tab navigation
  const tabLinks = document.querySelectorAll('.tab-link');
  if (tabLinks.length > 0) {
    tabLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('data-tab');
        
        // Hide all tab content
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.remove('active');
        });
        
        // Deactivate all tab links
        tabLinks.forEach(tabLink => {
          tabLink.classList.remove('active');
        });
        
        // Activate clicked tab and content
        link.classList.add('active');
        document.querySelector(`#${targetId}`).classList.add('active');
      });
    });
  }
}

/**
 * Start UI animations
 */
function startAnimations() {
  // Animate counters
  const counters = document.querySelectorAll('.counter');
  counters.forEach(counter => {
    const target = parseInt(counter.getAttribute('data-target'));
    const duration = 2000; // 2 seconds
    const step = target / (duration / 30); // Update every 30ms
    let current = 0;
    
    const updateCounter = () => {
      current += step;
      if (current < target) {
        counter.textContent = Math.floor(current);
        requestAnimationFrame(updateCounter);
      } else {
        counter.textContent = target;
      }
    };
    
    updateCounter();
  });
  
  // Animate cyber borders
  const cyberBorders = document.querySelectorAll('.cyber-border');
  cyberBorders.forEach(border => {
    // Already handled by CSS animations
  });
}

/**
 * Initialize terminal with typing effect
 */
function initTerminal(terminal) {
  const messages = [
    'Initializing Live Email Classifier...',
    'Loading NLP modules...',
    'Connecting to email server...',
    'Scanning for new messages...',
    'Analyzing email patterns...',
    'Classification system online.'
  ];
  
  const terminalContent = terminal.querySelector('.terminal-content');
  let messageIndex = 0;
  let charIndex = 0;
  
  function typeWriter() {
    if (messageIndex < messages.length) {
      const currentMessage = messages[messageIndex];
      
      if (charIndex < currentMessage.length) {
        terminalContent.innerHTML += currentMessage.charAt(charIndex);
        charIndex++;
        setTimeout(typeWriter, 50);
      } else {
        terminalContent.innerHTML += '<br><span class="terminal-prompt">></span> ';
        messageIndex++;
        charIndex = 0;
        setTimeout(typeWriter, 1000);
      }
    }
  }
  
  terminalContent.innerHTML = '<span class="terminal-prompt">></span> ';
  setTimeout(typeWriter, 1000);
}

/**
 * Load dashboard data
 */
function loadDashboardData() {
  // Simulate loading data
  showLoading(true);
  
  // In a real app, this would be an API call
  setTimeout(() => {
    // Update statistics
    updateStatistics({
      totalEmails: 1247,
      classifiedEmails: 1182,
      spamEmails: 523,
      hrEmails: 312,
      supportEmails: 347,
      accuracy: 96.8
    });
    
    // Update charts
    updateCharts();
    
    showLoading(false);
  }, 1500);
}

/**
 * Update dashboard statistics
 */
function updateStatistics(data) {
  const stats = document.querySelectorAll('.stat-value');
  stats.forEach(stat => {
    const key = stat.getAttribute('data-stat');
    if (data[key] !== undefined) {
      stat.textContent = data[key];
      
      // Add animation class
      stat.classList.add('neon-pulse');
      setTimeout(() => {
        stat.classList.remove('neon-pulse');
      }, 2000);
    }
  });
}

/**
 * Update dashboard charts
 */
function updateCharts() {
  // This would use a charting library like Chart.js
  console.log('Updating charts...');
}

/**
 * Load emails for the emails page
 */
function loadEmails() {
  showLoading(true);
  
  // In a real app, this would be an API call
  setTimeout(() => {
    const emailsContainer = document.querySelector('.emails-list');
    
    // Sample email data
    const emails = [
      {
        id: 'email-1',
        subject: 'Your account security alert',
        sender: '<EMAIL>',
        date: '2023-05-15 14:32',
        category: 'Spam',
        confidence: 0.92
      },
      {
        id: 'email-2',
        subject: 'Quarterly performance review',
        sender: '<EMAIL>',
        date: '2023-05-14 09:15',
        category: 'HR',
        confidence: 0.88
      },
      {
        id: 'email-3',
        subject: 'Ticket #45678 - Login issue',
        sender: '<EMAIL>',
        date: '2023-05-13 16:45',
        category: 'Support',
        confidence: 0.95
      }
    ];
    
    // Clear container
    emailsContainer.innerHTML = '';
    
    // Add emails to container
    emails.forEach(email => {
      const emailElement = createEmailElement(email);
      emailsContainer.appendChild(emailElement);
    });
    
    showLoading(false);
  }, 1500);
}

/**
 * Create email element for the emails list
 */
function createEmailElement(email) {
  const emailEl = document.createElement('div');
  emailEl.className = 'email-item';
  emailEl.setAttribute('data-id', email.id);
  
  // Get category color
  let categoryColor = 'var(--neon-blue)';
  if (email.category === 'Spam') {
    categoryColor = 'var(--glitch-red)';
  } else if (email.category === 'HR') {
    categoryColor = 'var(--neon-yellow)';
  } else if (email.category === 'Support') {
    categoryColor = 'var(--neon-green)';
  }
  
  emailEl.innerHTML = `
    <div class="email-header">
      <h3 class="email-subject">${email.subject}</h3>
      <span class="email-category" style="color: ${categoryColor};">${email.category}</span>
    </div>
    <div class="email-meta">
      <span class="email-sender">${email.sender}</span>
      <span class="email-date">${email.date}</span>
    </div>
    <div class="email-confidence">
      <div class="progress-bar" data-value="${email.confidence * 100}">
        <div class="progress-inner" style="width: ${email.confidence * 100}%; background-color: ${categoryColor};"></div>
      </div>
      <span class="confidence-value">${Math.round(email.confidence * 100)}% confidence</span>
    </div>
  `;
  
  return emailEl;
}

/**
 * Show/hide loading indicator
 */
function showLoading(show) {
  const loadingEl = document.querySelector('.loading-container');
  if (!loadingEl) return;
  
  if (show) {
    loadingEl.style.display = 'flex';
  } else {
    loadingEl.style.display = 'none';
  }
}

/**
 * Save settings from settings form
 */
function saveSettings(form) {
  showLoading(true);
  
  // Get form data
  const formData = new FormData(form);
  const settings = {};
  
  for (const [key, value] of formData.entries()) {
    settings[key] = value;
  }
  
  console.log('Saving settings:', settings);
  
  // In a real app, this would be an API call
  setTimeout(() => {
    showNotification('Settings saved successfully', 'success');
    showLoading(false);
  }, 1000);
}

/**
 * Classify email from form input
 */
function classifyEmail(form) {
  showLoading(true);
  
  // Get form data
  const formData = new FormData(form);
  const emailText = formData.get('email-text');
  
  console.log('Classifying email:', emailText);
  
  // In a real app, this would be an API call
  setTimeout(() => {
    // Sample classification result
    const result = {
      category: 'Support',
      confidence: 0.87,
      keywords: ['issue', 'help', 'support', 'problem']
    };
    
    displayClassificationResult(result);
    showLoading(false);
  }, 1500);
}

/**
 * Display email classification result
 */
function displayClassificationResult(result) {
  const resultContainer = document.querySelector('.classification-result');
  if (!resultContainer) return;
  
  // Get category color
  let categoryColor = 'var(--neon-blue)';
  if (result.category === 'Spam') {
    categoryColor = 'var(--glitch-red)';
  } else if (result.category === 'HR') {
    categoryColor = 'var(--neon-yellow)';
  } else if (result.category === 'Support') {
    categoryColor = 'var(--neon-green)';
  }
  
  resultContainer.innerHTML = `
    <div class="result-header">
      <h3>Classification Result</h3>
    </div>
    <div class="result-content">
      <div class="result-category">
        <span class="label">Category:</span>
        <span class="value" style="color: ${categoryColor};">${result.category}</span>
      </div>
      <div class="result-confidence">
        <span class="label">Confidence:</span>
        <div class="progress-bar" data-value="${result.confidence * 100}">
          <div class="progress-inner" style="width: ${result.confidence * 100}%; background-color: ${categoryColor};"></div>
        </div>
        <span class="confidence-value">${Math.round(result.confidence * 100)}%</span>
      </div>
      <div class="result-keywords">
        <span class="label">Keywords:</span>
        <div class="keywords-list">
          ${result.keywords.map(keyword => `<span class="keyword">${keyword}</span>`).join('')}
        </div>
      </div>
    </div>
  `;
  
  resultContainer.style.display = 'block';
  
  // Add animation
  resultContainer.classList.add('cyber-border');
  setTimeout(() => {
    resultContainer.scrollIntoView({ behavior: 'smooth' });
  }, 100);
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.innerHTML = `
    <div class="notification-content">
      <span class="notification-message">${message}</span>
    </div>
  `;
  
  document.body.appendChild(notification);
  
  // Show notification
  setTimeout(() => {
    notification.classList.add('show');
  }, 10);
  
  // Hide and remove notification
  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => {
      notification.remove();
    }, 300);
  }, 3000);
}
