"""
User model for the Live Email Classifier
"""

import datetime
from database import execute_query

class User:
    """User model class"""
    
    def __init__(self, id=None, email=None, name=None, profile_picture=None, 
                 access_token=None, refresh_token=None, token_expiry=None,
                 created_at=None, updated_at=None):
        self.id = id
        self.email = email
        self.name = name
        self.profile_picture = profile_picture
        self.access_token = access_token
        self.refresh_token = refresh_token
        self.token_expiry = token_expiry
        self.created_at = created_at
        self.updated_at = updated_at
    
    @staticmethod
    def get_by_id(user_id):
        """Get user by ID"""
        query = "SELECT * FROM users WHERE id = %s"
        results = execute_query(query, (user_id,), fetch=True)
        
        if results and len(results) > 0:
            result = results[0]
            return User(
                id=result['id'],
                email=result['email'],
                name=result['name'],
                profile_picture=result['profile_picture'],
                access_token=result['access_token'],
                refresh_token=result['refresh_token'],
                token_expiry=result['token_expiry'],
                created_at=result['created_at'],
                updated_at=result['updated_at']
            )
        
        return None
    
    @staticmethod
    def get_by_email(email):
        """Get user by email"""
        query = "SELECT * FROM users WHERE email = %s"
        results = execute_query(query, (email,), fetch=True)
        
        if results and len(results) > 0:
            result = results[0]
            return User(
                id=result['id'],
                email=result['email'],
                name=result['name'],
                profile_picture=result['profile_picture'],
                access_token=result['access_token'],
                refresh_token=result['refresh_token'],
                token_expiry=result['token_expiry'],
                created_at=result['created_at'],
                updated_at=result['updated_at']
            )
        
        return None
    
    def save(self):
        """Save user to database"""
        if self.id:
            # Update existing user
            query = """
                UPDATE users 
                SET email = %s, name = %s, profile_picture = %s, 
                    access_token = %s, refresh_token = %s, token_expiry = %s
                WHERE id = %s
            """
            params = (
                self.email, self.name, self.profile_picture,
                self.access_token, self.refresh_token, self.token_expiry,
                self.id
            )
            execute_query(query, params)
            return self.id
        else:
            # Create new user
            query = """
                INSERT INTO users 
                (email, name, profile_picture, access_token, refresh_token, token_expiry)
                VALUES (%s, %s, %s, %s, %s, %s)
            """
            params = (
                self.email, self.name, self.profile_picture,
                self.access_token, self.refresh_token, self.token_expiry
            )
            self.id = execute_query(query, params)
            return self.id
    
    @staticmethod
    def get_all():
        """Get all users"""
        query = "SELECT * FROM users"
        results = execute_query(query, fetch=True)
        
        users = []
        if results:
            for result in results:
                users.append(User(
                    id=result['id'],
                    email=result['email'],
                    name=result['name'],
                    profile_picture=result['profile_picture'],
                    access_token=result['access_token'],
                    refresh_token=result['refresh_token'],
                    token_expiry=result['token_expiry'],
                    created_at=result['created_at'],
                    updated_at=result['updated_at']
                ))
        
        return users

