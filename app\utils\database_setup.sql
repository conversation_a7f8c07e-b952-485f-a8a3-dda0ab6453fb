-- Database setup for Email Classifier

-- Create database
CREATE DATABASE IF NOT EXISTS email_classifier;

-- Use the database
USE email_classifier;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    name <PERSON><PERSON><PERSON><PERSON>(255),
    profile_picture VARCHAR(255),
    access_token TEXT,
    refresh_token TEXT,
    token_expiry DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create emails table
CREATE TABLE IF NOT EXISTS emails (
    id VARCHAR(255) PRIMARY KEY,
    user_id INT,
    thread_id VARCHAR(255),
    subject VA<PERSON><PERSON><PERSON>(255),
    sender VARCHAR(255),
    recipient VARCHAR(255),
    date DATETIME,
    snippet TEXT,
    body LONGTEXT,
    category VARCHAR(50),
    confidence FLOAT,
    is_important BOOLEAN DEFAULT FALSE,
    is_unread BOOLEAN DEFAULT FALSE,
    gmail_category VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create keywords table
CREATE TABLE IF NOT EXISTS keywords (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email_id VARCHAR(255),
    keyword VARCHAR(100),
    FOREIGN KEY (email_id) REFERENCES emails(id) ON DELETE CASCADE
);

-- Create labels table
CREATE TABLE IF NOT EXISTS labels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    name VARCHAR(100),
    color VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create email_labels table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS email_labels (
    email_id VARCHAR(255),
    label_id INT,
    PRIMARY KEY (email_id, label_id),
    FOREIGN KEY (email_id) REFERENCES emails(id) ON DELETE CASCADE,
    FOREIGN KEY (label_id) REFERENCES labels(id) ON DELETE CASCADE
);

-- Create settings table
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    setting_key VARCHAR(100),
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create statistics table
CREATE TABLE IF NOT EXISTS statistics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    total_emails INT DEFAULT 0,
    classified_emails INT DEFAULT 0,
    spam_emails INT DEFAULT 0,
    hr_emails INT DEFAULT 0,
    support_emails INT DEFAULT 0,
    casual_emails INT DEFAULT 0,
    accuracy FLOAT DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert default settings
INSERT INTO settings (user_id, setting_key, setting_value)
VALUES 
    (NULL, 'auto_apply_labels', 'true'),
    (NULL, 'check_interval', '60'),
    (NULL, 'unread_only', 'true'),
    (NULL, 'confidence_threshold', '0.7');

-- Insert default labels
INSERT INTO labels (user_id, name, color)
VALUES 
    (NULL, 'Spam', '#ff4444'),
    (NULL, 'HR', '#44aaff'),
    (NULL, 'Support', '#44ff44'),
    (NULL, 'Casual', '#ffaa44');
