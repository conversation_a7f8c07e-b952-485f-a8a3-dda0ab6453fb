<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gmail Login - Live Email Classifier</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/animations.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/hacker.css') }}">

    <style>
        .login-container {
            max-width: 500px;
            margin: 50px auto;
            padding: 30px;
            background-color: rgba(0, 0, 0, 0.7);
            border: 1px solid var(--hacker-green);
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-form .form-group {
            margin-bottom: 20px;
        }

        .login-form label {
            display: block;
            margin-bottom: 8px;
            color: var(--hacker-green);
        }

        .login-form input {
            width: 100%;
            padding: 12px;
            background-color: var(--hacker-dark);
            border: 1px solid var(--hacker-green);
            color: var(--hacker-green);
            font-family: var(--font-mono);
        }

        .login-form input:focus {
            outline: none;
            box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
        }

        .login-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
        }

        .login-options {
            margin-top: 20px;
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid var(--hacker-dark-green);
        }

        .login-options p {
            margin-bottom: 15px;
        }

        .error-message {
            color: var(--hacker-red);
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid var(--hacker-red);
            background-color: rgba(255, 0, 0, 0.1);
        }

        .info-message {
            color: var(--hacker-blue);
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid var(--hacker-blue);
            background-color: rgba(0, 102, 255, 0.1);
        }
    </style>
</head>
<body>
    <!-- Scanline Effect -->
    <div class="scanline"></div>

    <!-- Header -->
    <header class="header">
        <div class="logo">
            <span class="logo-text neon-pulse">LIVE EMAIL CLASSIFIER</span>
        </div>
    </header>

    <!-- Main Content -->
    <div class="main-content">
        <div class="login-container cyber-border">
            <div class="login-header">
                <h1 class="terminal-text">Gmail Authentication</h1>
                <p>Connect your Gmail account to analyze your emails</p>
            </div>

            {% if error %}
            <div class="error-message">
                <i class="fas fa-exclamation-triangle"></i> {{ error }}
            </div>
            {% endif %}

            {% if info %}
            <div class="info-message">
                <i class="fas fa-info-circle"></i> {{ info }}
            </div>
            {% endif %}

            <form class="login-form" action="{{ url_for('auth.gmail_auth') }}" method="post">
                <div class="form-group">
                    <label for="email">Gmail Address:</label>
                    <input type="email" id="email" name="email" placeholder="<EMAIL>" required>
                </div>

                <div class="form-group">
                    <label for="password">App Password:</label>
                    <input type="password" id="password" name="password" placeholder="Your app password" required>
                    <small style="color: var(--hacker-yellow); display: block; margin-top: 5px;">
                        <i class="fas fa-info-circle"></i> Use an <a href="https://support.google.com/accounts/answer/185833" target="_blank">App Password</a> for secure access
                    </small>
                </div>

                <div class="login-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i> Connect
                    </button>
                    <a href="{{ url_for('main.index') }}" class="btn btn-outline">
                        <i class="fas fa-arrow-left"></i> Back
                    </a>
                </div>
            </form>

            <div class="login-options">
                <p>Or connect using:</p>
                <a href="{{ url_for('auth.login') }}" class="btn btn-outline">
                    <i class="fab fa-google"></i> Google OAuth
                </a>
                <a href="{{ url_for('auth.demo_auth') }}" class="btn btn-outline">
                    <i class="fas fa-vial"></i> Demo Mode
                </a>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-container" style="display: none;">
        <div class="cyber-loading">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
        </div>
        <p class="loading-text neon-pulse">AUTHENTICATING...</p>
    </div>

    <!-- Scripts -->
    <script src="{{ url_for('static', filename='js/matrix.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const form = document.querySelector('.login-form');
            const loadingContainer = document.querySelector('.loading-container');

            form.addEventListener('submit', () => {
                loadingContainer.style.display = 'flex';
            });
        });
    </script>
</body>
</html>
