"""
Main application for the Live Email Classifier.
This script demonstrates the functionality of the email classifier.
"""

import os
import logging
import time
import pandas as pd
from typing import List, Dict, Any

from config import get_config, validate_config
from email_processor import EmailProcessor
from classifier import EmailClassifier, EmailPreprocessor, train_model

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('email_classifier.log')
    ]
)

logger = logging.getLogger(__name__)

def create_sample_training_data():
    """Create sample training data for demonstration purposes."""
    logger.info("Creating sample training data...")

    # Sample email texts and categories
    sample_data = [
        # Spam examples
        {"text": "Your account has been compromised. Click here to reset your password.", "category": "Spam"},
        {"text": "Congratulations! You've won a free iPhone. Claim now!", "category": "Spam"},
        {"text": "Limited time offer: 50% off all products. Shop now!", "category": "Spam"},
        {"text": "URGENT: Your payment is overdue. Click here to pay now.", "category": "Spam"},
        {"text": "You have been selected for a special offer. Act now!", "category": "Spam"},
        {"text": "Your account will be suspended unless you verify your information.", "category": "Spam"},
        {"text": "Get rich quick with this amazing investment opportunity.", "category": "Spam"},
        {"text": "Increase your followers on social media with our service.", "category": "Spam"},
        {"text": "You've been selected to participate in our survey. Win $500!", "category": "Spam"},
        {"text": "Your computer has a virus. Download our antivirus now.", "category": "Spam"},

        # HR examples
        {"text": "Your payroll information has been updated. Please review.", "category": "HR"},
        {"text": "Reminder: Complete your annual performance review by Friday.", "category": "HR"},
        {"text": "Company holiday schedule for the upcoming year is now available.", "category": "HR"},
        {"text": "Important update to the employee handbook. Please review.", "category": "HR"},
        {"text": "Your benefits enrollment period begins next week.", "category": "HR"},
        {"text": "Reminder about the upcoming team building event.", "category": "HR"},
        {"text": "Please submit your timesheet for the previous week.", "category": "HR"},
        {"text": "New training opportunities available. Sign up now.", "category": "HR"},
        {"text": "Changes to the company's remote work policy.", "category": "HR"},
        {"text": "Your vacation request has been approved.", "category": "HR"},

        # Support examples
        {"text": "Your support ticket #12345 has been resolved. Please confirm.", "category": "Support"},
        {"text": "We've received your inquiry about the product issue.", "category": "Support"},
        {"text": "Troubleshooting guide for common software issues.", "category": "Support"},
        {"text": "Your recent support request has been updated.", "category": "Support"},
        {"text": "How to reset your password: Step-by-step guide.", "category": "Support"},
        {"text": "FAQ: Frequently asked questions about our service.", "category": "Support"},
        {"text": "Your account access has been restored as requested.", "category": "Support"},
        {"text": "Tips for using our new software features.", "category": "Support"},
        {"text": "We're investigating the issue you reported.", "category": "Support"},
        {"text": "Your feedback has been received. Thank you for helping us improve.", "category": "Support"}
    ]

    # Create DataFrame
    df = pd.DataFrame(sample_data)

    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(config['classifier']['training_data']), exist_ok=True)

    # Save to CSV
    df.to_csv(config['classifier']['training_data'], index=False)
    logger.info(f"Sample training data saved to {config['classifier']['training_data']}")

    return df

def process_emails(email_processor: EmailProcessor, classifier: EmailClassifier,
                  preprocessor: EmailPreprocessor):
    """Process emails and classify them."""
    logger.info("Processing emails...")

    # Connect to email server
    if not email_processor.connect():
        logger.error("Failed to connect to email server")
        return

    # Get list of mailboxes
    mailboxes = email_processor.get_mailboxes()
    logger.info(f"Available mailboxes: {mailboxes}")

    # Fetch emails from inbox
    emails = email_processor.fetch_emails(
        mailbox=config['email']['mailbox'],
        limit=config['email']['fetch_limit'],
        unread_only=config['email']['unread_only']
    )

    logger.info(f"Fetched {len(emails)} emails")

    # Process each email
    for email_data in emails:
        # Preprocess email
        preprocessed_text = preprocessor.preprocess(email_data)

        # Classify email
        classification = classifier.classify(
            preprocessed_text,
            confidence_threshold=config['classifier']['confidence_threshold']
        )

        logger.info(f"Email: {email_data['subject']}")
        logger.info(f"Classification: {classification['category']} (Confidence: {classification['confidence']:.2f})")

        # Apply label if auto-labeling is enabled
        if config['app']['auto_apply_labels'] and classification['category'] != "Unknown":
            email_processor.apply_label(email_data['id'], classification['category'])
            logger.info(f"Applied label: {classification['category']}")

    # Disconnect from email server
    email_processor.disconnect()

def main():
    """Main function to run the email classifier."""
    logger.info("Starting Live Email Classifier...")

    # Validate configuration
    if not validate_config():
        logger.error("Invalid configuration. Please check your settings.")
        return

    # Create sample training data if it doesn't exist
    if not os.path.exists(config['classifier']['training_data']):
        create_sample_training_data()

    # Initialize classifier
    classifier = EmailClassifier(
        model_path=config['classifier']['model_path'],
        vectorizer_path=config['classifier']['vectorizer_path'],
        categories=config['classifier']['categories']
    )

    # Train model if it doesn't exist
    if not os.path.exists(config['classifier']['model_path']):
        logger.info("Training new model...")
        train_result = train_model(
            training_data_path=config['classifier']['training_data'],
            model_path=config['classifier']['model_path'],
            vectorizer_path=config['classifier']['vectorizer_path'],
            categories=config['classifier']['categories']
        )

        if train_result['success']:
            logger.info(f"Model trained successfully with accuracy: {train_result['accuracy']:.4f}")
        else:
            logger.error(f"Model training failed: {train_result.get('error', 'Unknown error')}")
            return

    # Load the model (whether it was just trained or already existed)
    if not classifier.load_model():
        logger.error("Failed to load model")
        return
    logger.info("Model loaded successfully")

    # Initialize preprocessor
    preprocessor = EmailPreprocessor()

    # Check if email credentials are provided
    if config['email']['email_address'] and config['email']['password']:
        # Initialize email processor
        email_processor = EmailProcessor(
            email_address=config['email']['email_address'],
            password=config['email']['password'],
            imap_server=config['email']['imap_server'],
            imap_port=config['email']['imap_port']
        )

        # Process emails
        process_emails(email_processor, classifier, preprocessor)
    else:
        logger.warning("Email credentials not provided. Skipping email processing.")

        # Demo classification with sample texts
        logger.info("Demo classification with sample texts:")
        sample_texts = [
            "Warning: Your email account will be locked unless you verify your identity now.",
            "Please review the updated company vacation policy for the upcoming summer.",
            "We have received your technical issue report and are working on a solution."
        ]

        for text in sample_texts:
            preprocessed_text = preprocessor.preprocess_text(text)
            classification = classifier.classify(
                preprocessed_text,
                confidence_threshold=config['classifier']['confidence_threshold']
            )

            logger.info(f"Text: {text}")
            logger.info(f"Classification: {classification['category']} (Confidence: {classification['confidence']:.2f})")

    logger.info("Live Email Classifier completed")

if __name__ == "__main__":
    # Get configuration
    config = get_config()

    try:
        main()
    except Exception as e:
        logger.error(f"Error in main application: {str(e)}", exc_info=True)
