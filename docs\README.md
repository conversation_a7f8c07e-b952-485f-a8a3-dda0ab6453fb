# Live Email Classifier

An automated email classification system that categorizes incoming emails into predefined categories (Spam, Professional, Casual) using Natural Language Processing with a cyberpunk-themed UI.

## Features

- Automatically classifies emails into categories
- Processes emails from your Gmail account in real-time
- Interactive visualization of email classification
- Secure OAuth 2.0 integration with Gmail
- Cyberpunk-themed UI with animations
- Includes a demo mode for testing without Gmail credentials

## Setup

### Prerequisites

1. Python 3.8 or higher
2. MySQL database
3. Google Cloud project with Gmail API enabled (for OAuth 2.0)

### Database Setup

1. Install MySQL if you haven't already
2. Run the database setup script:
   ```
   python setup_database.py
   ```

### OAuth 2.0 Configuration

1. Create a project in the [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the Gmail API
3. Configure the OAuth consent screen
4. Create OAuth 2.0 client credentials
5. Download the credentials as `client_secret.json` and place it in the project root

For detailed instructions, see [OAUTH_SETUP.md](OAUTH_SETUP.md).

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

### Running the Application

Run the Flask application:
```
python app.py
```

Then open your browser to http://localhost:5000

The application will:
- Present a cyberpunk-themed dashboard
- Allow you to connect to Gmail using OAuth 2.0
- Fetch and classify emails from your Gmail account
- Provide interactive visualizations of email classifications

If no OAuth credentials are provided, the application will run in demo mode with sample emails.

## Configuration Options

You can customize the behavior by editing the `settings.json` file or through the web interface:

```json
{
  "app": {
    "debug_mode": true,
    "auto_apply_labels": true,
    "log_level": "INFO"
  },
  "classifier": {
    "model_path": "data/model/email_classifier.pkl",
    "vectorizer_path": "data/model/vectorizer.pkl",
    "training_data": "data/training_data.csv",
    "categories": ["spam", "professional", "casual"],
    "confidence_threshold": 0.7
  },
  "model": {
    "algorithm": "naive_bayes",
    "test_size": 0.2,
    "random_state": 42,
    "max_features": 5000
  },
  "preprocessing": {
    "remove_stopwords": true,
    "stemming": true,
    "min_word_length": 3,
    "language": "english"
  }
}
```

## Presentation Mode

For presentation purposes, you can use the demo mode which doesn't require real Gmail credentials:

1. Run the application: `python app.py`
2. Open http://localhost:5000 in your browser
3. Click "Connect Gmail" and then "Demo Mode"
4. Use the sample emails to demonstrate the classification functionality
5. Explore the interactive visualization of email classifications

## Troubleshooting

- If the application fails to connect to your Gmail account, check your OAuth credentials
- Make sure your Google Cloud project has the Gmail API enabled
- Check that your `client_secret.json` file is correctly placed in the project root
- Check the `email_classifier.log` file for detailed error messages

## License

This software is provided as-is with no warranty. All rights reserved.
