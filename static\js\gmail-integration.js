/**
 * Gmail Integration for Live Email Classifier
 * Handles authentication and email fetching/analysis
 */

document.addEventListener('DOMContentLoaded', () => {
  // Initialize Gmail integration
  initGmailIntegration();

  // Add event listeners for Gmail-related buttons
  addGmailEventListeners();
});

/**
 * Initialize Gmail integration
 */
function initGmailIntegration() {
  console.log('Initializing Gmail integration...');

  // Check if user is authenticated
  const userInfoElement = document.querySelector('.user-info');
  if (userInfoElement && userInfoElement.dataset.authenticated === 'true') {
    // User is authenticated, update UI
    updateAuthenticatedUI(JSON.parse(userInfoElement.dataset.userInfo));
  } else {
    // User is not authenticated, show login button
    updateUnauthenticatedUI();
  }
}

/**
 * Add event listeners for Gmail-related buttons
 */
function addGmailEventListeners() {
  // Fetch emails button
  const fetchEmailsButton = document.querySelector('#fetch-emails');
  if (fetchEmailsButton) {
    fetchEmailsButton.addEventListener('click', () => {
      fetchAndAnalyzeEmails();
    });
  }

  // Analyze email form
  const analyzeForm = document.querySelector('#analyze-form');
  if (analyzeForm) {
    analyzeForm.addEventListener('submit', (e) => {
      e.preventDefault();
      analyzeEmail(analyzeForm);
    });
  }
}

/**
 * Update UI for authenticated user
 */
function updateAuthenticatedUI(userInfo) {
  // Update user info
  const userNameElement = document.querySelector('.user-name');
  if (userNameElement) {
    userNameElement.textContent = userInfo.email || 'AUTHENTICATED USER';
  }

  // Show authenticated elements
  document.querySelectorAll('.authenticated-only').forEach(el => {
    el.style.display = 'block';
  });

  // Hide unauthenticated elements
  document.querySelectorAll('.unauthenticated-only').forEach(el => {
    el.style.display = 'none';
  });

  // Update terminal with authentication message
  const terminal = document.querySelector('.terminal-content');
  if (terminal) {
    terminal.innerHTML += '<br><span class="terminal-prompt">></span> Authenticated with Gmail as ' + userInfo.email;
    terminal.innerHTML += '<br><span class="terminal-prompt">></span> Ready to analyze emails...';
  }
}

/**
 * Update UI for unauthenticated user
 */
function updateUnauthenticatedUI() {
  // Show unauthenticated elements
  document.querySelectorAll('.unauthenticated-only').forEach(el => {
    el.style.display = 'block';
  });

  // Hide authenticated elements
  document.querySelectorAll('.authenticated-only').forEach(el => {
    el.style.display = 'none';
  });
}

/**
 * Fetch and analyze emails from Gmail
 */
function fetchAndAnalyzeEmails() {
  showLoading(true);

  // Fetch emails from API
  fetch('/api/emails')
    .then(response => response.json())
    .then(emails => {
      // Update emails list
      updateEmailsList(emails);
      showLoading(false);
    })
    .catch(error => {
      console.error('Error fetching emails:', error);
      showNotification('Error fetching emails: ' + error.message, 'error');
      showLoading(false);
    });
}

/**
 * Update emails list with analyzed emails
 */
function updateEmailsList(emails) {
  const emailsContainer = document.querySelector('.emails-list');
  if (!emailsContainer) return;

  // Clear container
  emailsContainer.innerHTML = '';

  if (emails.length === 0) {
    emailsContainer.innerHTML = '<div class="no-emails">No emails found</div>';
    return;
  }

  // Add emails to container
  emails.forEach(email => {
    const emailElement = createEmailElement(email);
    emailsContainer.appendChild(emailElement);
  });

  // Show notification
  showNotification(`Analyzed ${emails.length} emails`, 'success');
}

/**
 * Create email element with analysis results
 */
function createEmailElement(email) {
  const emailEl = document.createElement('div');
  emailEl.className = 'email-item';
  emailEl.setAttribute('data-id', email.id);

  // Get category color
  let categoryColor = 'var(--hacker-green)';
  if (email.category === 'spam') {
    categoryColor = 'var(--hacker-red)';
  } else if (email.category === 'professional') {
    categoryColor = 'var(--hacker-blue)';
  } else if (email.category === 'casual') {
    categoryColor = 'var(--hacker-yellow)';
  }

  emailEl.innerHTML = `
    <div class="email-header">
      <h3 class="email-subject">${email.subject}</h3>
      <span class="email-category" style="color: ${categoryColor};">${email.category}</span>
    </div>
    <div class="email-meta">
      <span class="email-sender">${email.sender}</span>
      <span class="email-date">${email.date}</span>
    </div>
    <div class="email-snippet">${email.snippet}</div>
    <div class="email-confidence">
      <div class="progress-bar" data-value="${email.confidence * 100}">
        <div class="progress-inner" style="width: ${email.confidence * 100}%; background-color: ${categoryColor};"></div>
      </div>
      <span class="confidence-value">${Math.round(email.confidence * 100)}% confidence</span>
    </div>
    <div class="email-keywords">
      ${email.keywords.map(keyword => `<span class="keyword">${keyword}</span>`).join('')}
    </div>
  `;

  return emailEl;
}

/**
 * Analyze a single email
 */
function analyzeEmail(form) {
  showLoading(true);

  // Get form data
  const formData = new FormData(form);
  const emailText = formData.get('email-text');
  const subject = formData.get('email-subject') || '';

  if (!emailText) {
    showNotification('Please enter email text', 'error');
    showLoading(false);
    return;
  }

  // Prepare request data
  const requestData = {
    email_text: emailText,
    subject: subject
  };

  // Send request to API
  fetch('/api/analyze', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(requestData)
  })
    .then(response => response.json())
    .then(result => {
      // Display result
      displayAnalysisResult(result);
      showLoading(false);
    })
    .catch(error => {
      console.error('Error analyzing email:', error);
      showNotification('Error analyzing email: ' + error.message, 'error');
      showLoading(false);
    });
}

/**
 * Display email analysis result
 */
function displayAnalysisResult(result) {
  const resultContainer = document.querySelector('#analysis-result');
  if (!resultContainer) return;

  // Get category color
  let categoryColor = 'var(--hacker-green)';
  if (result.category === 'spam') {
    categoryColor = 'var(--hacker-red)';
  } else if (result.category === 'professional') {
    categoryColor = 'var(--hacker-blue)';
  } else if (result.category === 'casual') {
    categoryColor = 'var(--hacker-yellow)';
  }

  // Create probabilities HTML
  let probabilitiesHtml = '';
  if (result.probabilities) {
    probabilitiesHtml = '<div class="probabilities">';
    for (const [category, probability] of Object.entries(result.probabilities)) {
      const width = Math.round(probability * 100);
      let color = 'var(--hacker-green)';
      if (category === 'spam') color = 'var(--hacker-red)';
      if (category === 'professional') color = 'var(--hacker-blue)';
      if (category === 'casual') color = 'var(--hacker-yellow)';

      probabilitiesHtml += `
        <div class="probability-item">
          <span class="probability-label">${category}:</span>
          <div class="probability-bar">
            <div class="probability-fill" style="width: ${width}%; background-color: ${color};"></div>
          </div>
          <span class="probability-value">${width}%</span>
        </div>
      `;
    }
    probabilitiesHtml += '</div>';
  }

  // Update result container
  resultContainer.innerHTML = `
    <div class="result-header">
      <h3>Analysis Result</h3>
    </div>
    <div class="result-content">
      <div class="result-category">
        <span class="label">Category:</span>
        <span class="value" style="color: ${categoryColor};">${result.category}</span>
      </div>
      <div class="result-confidence">
        <span class="label">Confidence:</span>
        <div class="progress-bar" data-value="${result.confidence * 100}">
          <div class="progress-inner" style="width: ${result.confidence * 100}%; background-color: ${categoryColor};"></div>
        </div>
        <span class="confidence-value">${Math.round(result.confidence * 100)}%</span>
      </div>
      ${probabilitiesHtml}
      <div class="result-keywords">
        <span class="label">Keywords:</span>
        <div class="keywords-list">
          ${result.keywords.map(keyword => `<span class="keyword">${keyword}</span>`).join('')}
        </div>
      </div>
    </div>
  `;

  resultContainer.style.display = 'block';

  // Add animation
  resultContainer.classList.add('cyber-border');
  setTimeout(() => {
    resultContainer.scrollIntoView({ behavior: 'smooth' });
  }, 100);

  // Trigger visualization if the function exists
  if (typeof window.visualizeClassification === 'function') {
    window.visualizeClassification(result.category, result.confidence);
  }
}
