# 🔧 Manual Credential Setup Guide

## 📋 Quick Setup (2 Minutes)

### **Step 1: Get Your Credentials from Google Cloud Console**

Go to: https://console.cloud.google.com/apis/credentials

Find your OAuth 2.0 Client ID and copy these values:

1. **Project ID**: (shown at top of page)
2. **Client ID**: (ends with `.apps.googleusercontent.com`)
3. **Client Secret**: (starts with `GOCSPX-`)

### **Step 2: Update the Credentials File**

Edit the file: `config/client_secret.json`

Replace the template values with your real credentials:

```json
{
  "web": *************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
}
```

### **Step 3: Test the Setup**

Run the test script:
```bash
python test_gmail_oauth.py
```

### **Step 4: Restart the Application**

```bash
python run_app.py
```

### **Step 5: Test OAuth Flow**

1. Go to: http://localhost:8080
2. Click **"Google OAuth"** (not Demo Mode)
3. Complete the OAuth flow
4. Grant Gmail permissions
5. Verify real Gmail data loads

## 🎯 **Example Values**

**Before (Template):**
```json
"client_id": "YOUR_CLIENT_ID.apps.googleusercontent.com"
"project_id": "your-project-id"
"client_secret": "YOUR_CLIENT_SECRET"
```

**After (Real Credentials):**
```json
"client_id": "*********-abcdefghijklmnop.apps.googleusercontent.com"
"project_id": "live-email-classifier-12345"
"client_secret": "GOCSPX-AbCdEfGhIjKlMnOpQrStUvWxYz"
```

## ✅ **Success Indicators**

- ✅ Test script passes all checks
- ✅ "Google OAuth" button redirects to Google
- ✅ OAuth flow completes successfully
- ✅ Real Gmail emails appear (not demo data)

## 🚨 **Common Issues**

**❌ redirect_uri_mismatch**
- Check Google Console has: `http://localhost:8080/oauth2callback`

**❌ invalid_client**
- Verify credentials are copied correctly
- Check for extra spaces or missing characters

**❌ access_denied**
- Make sure OAuth consent screen is configured
- Add yourself as a test user

---

**🎊 Once configured, you'll have full Gmail integration!**
