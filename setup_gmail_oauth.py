#!/usr/bin/env python3
"""
Interactive Gmail OAuth Setup Assistant
Helps users set up Google OAuth 2.0 credentials for the Live Email Classifier
"""

import os
import json
import webbrowser
from pathlib import Path

def print_banner():
    """Print the setup banner."""
    print("=" * 60)
    print("🚀 LIVE EMAIL CLASSIFIER - GMAIL SETUP ASSISTANT")
    print("=" * 60)
    print()

def check_current_setup():
    """Check the current OAuth setup status."""
    print("📋 Checking current setup...")
    
    config_path = Path("config/client_secret.json")
    
    if not config_path.exists():
        print("❌ client_secret.json not found")
        return False
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        client_id = config.get('web', {}).get('client_id', '')
        client_secret = config.get('web', {}).get('client_secret', '')
        
        if 'YOUR_CLIENT_ID' in client_id or 'YOUR_CLIENT_SECRET' in client_secret:
            print("⚠️  Template file found - needs real credentials")
            return False
        else:
            print("✅ Valid OAuth credentials found!")
            print(f"   Client ID: {client_id[:20]}...")
            return True
            
    except Exception as e:
        print(f"❌ Error reading config: {e}")
        return False

def open_google_console():
    """Open Google Cloud Console."""
    print("\n🌐 Opening Google Cloud Console...")
    webbrowser.open("https://console.cloud.google.com/")
    print("✅ Google Cloud Console opened in your browser")

def show_setup_steps():
    """Show the setup steps."""
    print("\n📝 SETUP STEPS:")
    print()
    print("1️⃣  CREATE GOOGLE CLOUD PROJECT")
    print("   • Go to Google Cloud Console (opened in browser)")
    print("   • Click 'New Project'")
    print("   • Name: 'Live Email Classifier'")
    print("   • Click 'Create'")
    print()
    
    print("2️⃣  ENABLE GMAIL API")
    print("   • Go to 'APIs & Services' > 'Library'")
    print("   • Search for 'Gmail API'")
    print("   • Click 'Enable'")
    print()
    
    print("3️⃣  CONFIGURE OAUTH CONSENT SCREEN")
    print("   • Go to 'APIs & Services' > 'OAuth consent screen'")
    print("   • Select 'External' user type")
    print("   • App name: 'Live Email Classifier'")
    print("   • Add your email as support contact")
    print("   • Add required scopes:")
    print("     - https://www.googleapis.com/auth/gmail.readonly")
    print("     - https://www.googleapis.com/auth/gmail.labels")
    print("     - https://www.googleapis.com/auth/gmail.metadata")
    print("     - https://www.googleapis.com/auth/userinfo.email")
    print("   • Add yourself as test user")
    print()
    
    print("4️⃣  CREATE OAUTH CLIENT ID")
    print("   • Go to 'APIs & Services' > 'Credentials'")
    print("   • Click 'Create Credentials' > 'OAuth client ID'")
    print("   • Application type: 'Web application'")
    print("   • Authorized JavaScript origins: http://localhost:8080")
    print("   • Authorized redirect URIs: http://localhost:8080/oauth2callback")
    print("   • Click 'Create'")
    print("   • Download the JSON file")
    print()
    
    print("5️⃣  INSTALL CREDENTIALS")
    print("   • Rename downloaded file to 'client_secret.json'")
    print("   • Move to: config/client_secret.json")
    print("   • Restart the application")

def validate_credentials():
    """Validate the OAuth credentials."""
    print("\n🔍 Validating credentials...")
    
    config_path = Path("config/client_secret.json")
    
    if not config_path.exists():
        print("❌ client_secret.json not found in config/ directory")
        return False
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        web_config = config.get('web', {})
        client_id = web_config.get('client_id', '')
        client_secret = web_config.get('client_secret', '')
        redirect_uris = web_config.get('redirect_uris', [])
        
        # Check for template values
        if 'YOUR_CLIENT_ID' in client_id:
            print("❌ Client ID still contains template value")
            return False
        
        if 'YOUR_CLIENT_SECRET' in client_secret:
            print("❌ Client Secret still contains template value")
            return False
        
        # Check redirect URI
        expected_uri = "http://localhost:8080/oauth2callback"
        if expected_uri not in redirect_uris:
            print(f"⚠️  Warning: Expected redirect URI not found")
            print(f"   Expected: {expected_uri}")
            print(f"   Found: {redirect_uris}")
        
        print("✅ Credentials look valid!")
        print(f"   Client ID: {client_id[:20]}...")
        print(f"   Project ID: {web_config.get('project_id', 'N/A')}")
        return True
        
    except Exception as e:
        print(f"❌ Error validating credentials: {e}")
        return False

def main():
    """Main setup function."""
    print_banner()
    
    # Check current setup
    if check_current_setup():
        print("\n🎉 Gmail OAuth is already configured!")
        print("   Your application should be able to connect to Gmail.")
        print("   Try clicking 'Google OAuth' in the application.")
        return
    
    print("\n🔧 Gmail OAuth setup required...")
    
    # Ask user if they want to proceed
    response = input("\nWould you like to set up Gmail OAuth now? (y/n): ").lower().strip()
    
    if response != 'y':
        print("Setup cancelled. You can run this script again anytime.")
        return
    
    # Open Google Console and show steps
    open_google_console()
    show_setup_steps()
    
    print("\n" + "=" * 60)
    print("⏳ Complete the steps above, then press Enter to validate...")
    input()
    
    # Validate credentials
    if validate_credentials():
        print("\n🎉 SUCCESS! Gmail OAuth is now configured!")
        print("\n🚀 Next steps:")
        print("   1. Restart your Live Email Classifier application")
        print("   2. Click 'Google OAuth' to test the connection")
        print("   3. Grant permissions when prompted")
        print("   4. Enjoy real Gmail integration!")
    else:
        print("\n❌ Setup incomplete. Please check the steps above and try again.")
        print("   Run this script again after completing the setup.")

if __name__ == "__main__":
    main()
