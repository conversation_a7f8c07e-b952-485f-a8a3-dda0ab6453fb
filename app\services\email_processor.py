"""
Email Processor Module for Live Email Classifier.
This module handles the connection to email servers and fetching of emails.
"""

import imaplib
import email
from email.header import decode_header
import os
import re
from datetime import datetime
import logging
from typing import List, Dict, Any, Tuple, Optional

logger = logging.getLogger(__name__)

class EmailProcessor:
    """Class to handle email processing operations."""

    def __init__(self, email_address: str, password: str, imap_server: str = "imap.gmail.com",
                 imap_port: int = 993):
        """
        Initialize the EmailProcessor with email credentials.

        Args:
            email_address: Email address to connect with
            password: Password or app password for the email account
            imap_server: IMAP server address (default: imap.gmail.com)
            imap_port: IMAP server port (default: 993)
        """
        self.email_address = email_address
        self.password = password
        self.imap_server = imap_server
        self.imap_port = imap_port
        self.connection = None

    def connect(self) -> bool:
        """
        Establish connection to the IMAP server.

        Returns:
            bool: True if connection is successful, False otherwise
        """
        try:
            self.connection = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            self.connection.login(self.email_address, self.password)
            return True
        except Exception as e:
            logger.error(f"Failed to connect to email server: {str(e)}")
            return False

    def disconnect(self) -> None:
        """Close the connection to the IMAP server."""
        if self.connection:
            try:
                self.connection.logout()
            except Exception as e:
                logger.error(f"Error during disconnect: {str(e)}")

    def get_mailboxes(self) -> List[str]:
        """
        Get list of available mailboxes/folders.

        Returns:
            List[str]: List of mailbox names
        """
        if not self.connection:
            if not self.connect():
                return []

        mailboxes = []
        try:
            status, mailbox_list = self.connection.list()
            if status == 'OK':
                for mailbox in mailbox_list:
                    mailbox_name = mailbox.decode().split('"')[-2]
                    mailboxes.append(mailbox_name)
        except Exception as e:
            logger.error(f"Error getting mailboxes: {str(e)}")

        return mailboxes

    def fetch_emails(self, mailbox: str = "INBOX", limit: int = 10,
                     unread_only: bool = False) -> List[Dict[str, Any]]:
        """
        Fetch emails from the specified mailbox.

        Args:
            mailbox: Name of the mailbox to fetch from (default: INBOX)
            limit: Maximum number of emails to fetch (default: 10)
            unread_only: Whether to fetch only unread emails (default: False)

        Returns:
            List[Dict[str, Any]]: List of email data dictionaries
        """
        if not self.connection:
            if not self.connect():
                return []

        emails = []
        try:
            # Select the mailbox
            status, messages = self.connection.select(mailbox)
            if status != 'OK':
                logger.error(f"Failed to select mailbox {mailbox}")
                return []

            # Get email IDs
            search_criteria = '(UNSEEN)' if unread_only else 'ALL'
            status, email_ids = self.connection.search(None, search_criteria)
            if status != 'OK':
                logger.error("Failed to search for emails")
                return []

            # Convert email IDs to a list and get the most recent ones
            email_id_list = email_ids[0].split()
            email_id_list = email_id_list[-limit:] if limit < len(email_id_list) else email_id_list

            # Fetch each email
            for email_id in reversed(email_id_list):
                status, msg_data = self.connection.fetch(email_id, '(RFC822)')
                if status != 'OK':
                    logger.error(f"Failed to fetch email with ID {email_id}")
                    continue

                raw_email = msg_data[0][1]
                email_message = email.message_from_bytes(raw_email)

                # Extract email data
                email_data = self._parse_email(email_message, email_id)
                emails.append(email_data)

        except Exception as e:
            logger.error(f"Error fetching emails: {str(e)}")

        return emails

    def _parse_email(self, email_message,
                    email_id: bytes) -> Dict[str, Any]:
        """
        Parse an email message into a structured dictionary.

        Args:
            email_message: The email message object
            email_id: The ID of the email

        Returns:
            Dict[str, Any]: Structured email data
        """
        # Get email headers
        subject = self._decode_header(email_message.get('Subject', ''))
        from_addr = self._decode_header(email_message.get('From', ''))
        to_addr = self._decode_header(email_message.get('To', ''))
        date_str = email_message.get('Date', '')

        # Parse date
        try:
            date = datetime.strptime(date_str, "%a, %d %b %Y %H:%M:%S %z")
        except:
            date = None

        # Get email body
        body = ""
        if email_message.is_multipart():
            for part in email_message.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))

                # Skip attachments
                if "attachment" in content_disposition:
                    continue

                # Get text content
                if content_type == "text/plain":
                    try:
                        body_part = part.get_payload(decode=True).decode()
                        body += body_part
                    except:
                        pass
        else:
            # If not multipart, just get the payload
            try:
                body = email_message.get_payload(decode=True).decode()
            except:
                body = ""

        # Create structured email data
        email_data = {
            "id": email_id.decode(),
            "subject": subject,
            "from": from_addr,
            "to": to_addr,
            "date": date,
            "body": body,
            "raw_headers": dict(email_message.items())
        }

        return email_data

    def _decode_header(self, header: str) -> str:
        """
        Decode email header.

        Args:
            header: The header string to decode

        Returns:
            str: Decoded header string
        """
        decoded_header = ""
        try:
            decoded_parts = decode_header(header)
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        decoded_header += part.decode(encoding)
                    else:
                        decoded_header += part.decode('utf-8', errors='replace')
                else:
                    decoded_header += str(part)
        except Exception as e:
            logger.error(f"Error decoding header: {str(e)}")
            decoded_header = header

        return decoded_header

    def mark_as_read(self, email_id: str) -> bool:
        """
        Mark an email as read.

        Args:
            email_id: ID of the email to mark as read

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.connection:
            if not self.connect():
                return False

        try:
            self.connection.store(email_id.encode(), '+FLAGS', '\\Seen')
            return True
        except Exception as e:
            logger.error(f"Error marking email as read: {str(e)}")
            return False

    def apply_label(self, email_id: str, label: str) -> bool:
        """
        Apply a label to an email (Gmail-specific).

        Args:
            email_id: ID of the email to label
            label: Label to apply

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.connection:
            if not self.connect():
                return False

        try:
            # Gmail uses X-GM-LABELS for labels
            self.connection.store(email_id.encode(), '+X-GM-LABELS', label)
            return True
        except Exception as e:
            logger.error(f"Error applying label: {str(e)}")
            return False
