#!/usr/bin/env python3
"""
Credential Installation Helper
Helps install the downloaded Google OAuth credentials
"""

import os
import json
import shutil
from pathlib import Path

def find_downloaded_credentials():
    """Find the downloaded credentials file."""
    downloads_dir = Path.home() / "Downloads"
    
    # Look for client_secret files
    pattern = "client_secret_*.json"
    files = list(downloads_dir.glob(pattern))
    
    if not files:
        return None
    
    # Return the most recent one
    return max(files, key=os.path.getctime)

def install_credentials():
    """Install the credentials file."""
    print("🔍 Looking for downloaded credentials...")
    
    # Find the downloaded file
    cred_file = find_downloaded_credentials()
    
    if not cred_file:
        print("❌ No credentials file found in Downloads folder")
        print("   Please download the JSON file from Google Cloud Console")
        print("   File should be named: client_secret_*.json")
        return False
    
    print(f"✅ Found credentials: {cred_file.name}")
    
    # Validate the file
    try:
        with open(cred_file, 'r') as f:
            config = json.load(f)
        
        if 'web' not in config:
            print("❌ Invalid credentials file format")
            return False
        
        client_id = config['web'].get('client_id', '')
        if not client_id or 'YOUR_CLIENT_ID' in client_id:
            print("❌ Credentials file appears to be a template")
            return False
        
        print(f"✅ Valid credentials for: {client_id[:30]}...")
        
    except Exception as e:
        print(f"❌ Error reading credentials: {e}")
        return False
    
    # Install the file
    target_path = Path("config/client_secret.json")
    
    try:
        # Create config directory if it doesn't exist
        target_path.parent.mkdir(exist_ok=True)
        
        # Copy the file
        shutil.copy2(cred_file, target_path)
        
        print(f"✅ Credentials installed to: {target_path}")
        print("✅ Gmail OAuth setup complete!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error installing credentials: {e}")
        return False

def main():
    """Main function."""
    print("=" * 50)
    print("🔧 CREDENTIAL INSTALLATION HELPER")
    print("=" * 50)
    print()
    
    if install_credentials():
        print("\n🎉 SUCCESS!")
        print("   Your Gmail OAuth credentials are now installed.")
        print("\n🚀 Next steps:")
        print("   1. Restart your Live Email Classifier application")
        print("   2. Click 'Google OAuth' to test the connection")
        print("   3. Grant permissions when prompted")
        print("   4. Enjoy real Gmail integration!")
    else:
        print("\n❌ Installation failed.")
        print("   Please check the steps above and try again.")

if __name__ == "__main__":
    main()
