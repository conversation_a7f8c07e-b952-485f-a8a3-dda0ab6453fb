"""
Email Classifier Model for Live Email Classifier.
This module contains the EmailClassifier class for classifying emails.
"""

import pickle
import numpy as np
import logging
from typing import Dict, List, Any, Tuple, Optional
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
import os

logger = logging.getLogger(__name__)

class EmailClassifier:
    """Class for classifying emails into predefined categories."""
    
    def __init__(self, model_path: str = None, vectorizer_path: str = None, 
                 categories: List[str] = None):
        """
        Initialize the EmailClassifier.
        
        Args:
            model_path: Path to the saved model file (default: None)
            vectorizer_path: Path to the saved vectorizer file (default: None)
            categories: List of categories for classification (default: None)
        """
        self.model_path = model_path
        self.vectorizer_path = vectorizer_path
        self.categories = categories or ['Spam', 'HR', 'Support']
        self.model = None
        self.vectorizer = None
        
        # Try to load the model and vectorizer if paths are provided
        if model_path and vectorizer_path:
            self.load_model()
    
    def load_model(self) -> bool:
        """
        Load the classifier model and vectorizer from disk.
        
        Returns:
            bool: True if loading was successful, False otherwise
        """
        try:
            if os.path.exists(self.model_path) and os.path.exists(self.vectorizer_path):
                with open(self.model_path, 'rb') as f:
                    self.model = pickle.load(f)
                
                with open(self.vectorizer_path, 'rb') as f:
                    self.vectorizer = pickle.load(f)
                
                logger.info("Model and vectorizer loaded successfully")
                return True
            else:
                logger.warning("Model or vectorizer file not found")
                return False
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            return False
    
    def save_model(self) -> bool:
        """
        Save the classifier model and vectorizer to disk.
        
        Returns:
            bool: True if saving was successful, False otherwise
        """
        try:
            # Create directories if they don't exist
            os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
            os.makedirs(os.path.dirname(self.vectorizer_path), exist_ok=True)
            
            with open(self.model_path, 'wb') as f:
                pickle.dump(self.model, f)
            
            with open(self.vectorizer_path, 'wb') as f:
                pickle.dump(self.vectorizer, f)
            
            logger.info("Model and vectorizer saved successfully")
            return True
        except Exception as e:
            logger.error(f"Error saving model: {str(e)}")
            return False
    
    def classify(self, text: str, confidence_threshold: float = 0.7) -> Dict[str, Any]:
        """
        Classify the given text into one of the predefined categories.
        
        Args:
            text: The text to classify
            confidence_threshold: Minimum confidence threshold for classification
            
        Returns:
            Dict[str, Any]: Classification result with category and confidence
        """
        if not self.model or not self.vectorizer:
            logger.error("Model or vectorizer not loaded")
            return {"category": "Unknown", "confidence": 0.0}
        
        try:
            # Vectorize the text
            text_vectorized = self.vectorizer.transform([text])
            
            # Get prediction probabilities
            probabilities = self.model.predict_proba(text_vectorized)[0]
            
            # Get the highest probability and its index
            max_prob = max(probabilities)
            max_prob_index = np.argmax(probabilities)
            
            # Get the predicted category
            predicted_category = self.categories[max_prob_index] if max_prob >= confidence_threshold else "Unknown"
            
            return {
                "category": predicted_category,
                "confidence": float(max_prob),
                "probabilities": {cat: float(prob) for cat, prob in zip(self.categories, probabilities)}
            }
        except Exception as e:
            logger.error(f"Error during classification: {str(e)}")
            return {"category": "Unknown", "confidence": 0.0}
