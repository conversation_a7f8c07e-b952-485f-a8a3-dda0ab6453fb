#!/usr/bin/env python3
"""
Gmail Credentials Configurator
Interactive tool to set up real Gmail OAuth credentials
"""

import json
import os
from pathlib import Path

def print_banner():
    """Print the configuration banner."""
    print("=" * 60)
    print("🔧 GMAIL CREDENTIALS CONFIGURATOR")
    print("=" * 60)
    print()

def get_credentials_from_user():
    """Get OAuth credentials from user input."""
    print("📋 Please provide your Google OAuth credentials:")
    print("   (You can find these in your Google Cloud Console)")
    print()
    
    # Get project ID
    project_id = input("🔹 Project ID: ").strip()
    if not project_id:
        print("❌ Project ID is required")
        return None
    
    # Get client ID
    print("\n🔹 Client ID (ends with .apps.googleusercontent.com):")
    client_id = input("   ").strip()
    if not client_id or not client_id.endswith('.apps.googleusercontent.com'):
        print("❌ Invalid Client ID format")
        return None
    
    # Get client secret
    print("\n🔹 Client Secret (starts with GOCSPX-):")
    client_secret = input("   ").strip()
    if not client_secret:
        print("❌ Client Secret is required")
        return None
    
    return {
        'project_id': project_id,
        'client_id': client_id,
        'client_secret': client_secret
    }

def create_credentials_file(credentials):
    """Create the credentials file."""
    config = {
        "web": {
            "client_id": credentials['client_id'],
            "project_id": credentials['project_id'],
            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
            "token_uri": "https://oauth2.googleapis.com/token",
            "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
            "client_secret": credentials['client_secret'],
            "redirect_uris": ["http://localhost:8080/oauth2callback"],
            "javascript_origins": ["http://localhost:8080"]
        }
    }
    
    # Ensure config directory exists
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    # Write the file
    config_path = config_dir / "client_secret.json"
    
    try:
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ Credentials saved to: {config_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error saving credentials: {e}")
        return False

def validate_credentials():
    """Validate the created credentials."""
    config_path = Path("config/client_secret.json")
    
    if not config_path.exists():
        return False
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        web_config = config.get('web', {})
        client_id = web_config.get('client_id', '')
        client_secret = web_config.get('client_secret', '')
        
        # Check for template values
        if 'YOUR_CLIENT_ID' in client_id or 'YOUR_CLIENT_SECRET' in client_secret:
            return False
        
        print("✅ Credentials validation passed!")
        print(f"   Client ID: {client_id[:30]}...")
        print(f"   Project ID: {web_config.get('project_id', 'N/A')}")
        return True
        
    except Exception as e:
        print(f"❌ Validation error: {e}")
        return False

def show_next_steps():
    """Show what to do next."""
    print("\n🚀 NEXT STEPS:")
    print("1. Restart your Live Email Classifier application")
    print("2. Go to: http://localhost:8080")
    print("3. Click 'Google OAuth' (not Demo Mode)")
    print("4. Complete the OAuth flow with Google")
    print("5. Grant permissions to access your Gmail")
    print("6. Enjoy real Gmail integration!")

def main():
    """Main configuration function."""
    print_banner()
    
    print("🔍 This tool will help you configure real Gmail OAuth credentials.")
    print("   Make sure you have:")
    print("   ✅ Created a Google Cloud project")
    print("   ✅ Enabled Gmail API")
    print("   ✅ Created OAuth 2.0 credentials")
    print()
    
    # Ask if user wants to proceed
    response = input("Ready to configure credentials? (y/n): ").lower().strip()
    if response != 'y':
        print("Configuration cancelled.")
        return
    
    # Get credentials from user
    credentials = get_credentials_from_user()
    if not credentials:
        print("\n❌ Configuration failed. Please try again.")
        return
    
    # Create credentials file
    print("\n💾 Creating credentials file...")
    if not create_credentials_file(credentials):
        print("\n❌ Failed to create credentials file.")
        return
    
    # Validate credentials
    print("\n🔍 Validating credentials...")
    if not validate_credentials():
        print("\n❌ Credentials validation failed.")
        return
    
    print("\n🎉 SUCCESS! Gmail OAuth credentials configured!")
    show_next_steps()

if __name__ == "__main__":
    main()
