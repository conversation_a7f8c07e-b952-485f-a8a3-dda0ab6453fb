"""
Live Email Classifier Application Package
"""

from flask import Flask
import os
import secrets
import logging

def create_app():
    """Application factory pattern for creating Flask app."""

    # Add config path to sys.path
    import sys
    config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config')
    if config_path not in sys.path:
        sys.path.insert(0, config_path)

    # Initialize Flask app
    app = Flask(__name__,
                static_folder='../static',
                template_folder='../templates')

    # Configure app
    app.secret_key = secrets.token_hex(16)
    app.config['SESSION_TYPE'] = 'filesystem'
    app.config['OAUTH_REDIRECT_URI'] = 'http://localhost:8080/oauth2callback'

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('email_classifier.log')
        ]
    )

    # Register blueprints
    from app.routes.main import main_bp
    from app.routes.auth import auth_bp
    from app.routes.api import api_bp

    app.register_blueprint(main_bp)
    app.register_blueprint(auth_bp)
    app.register_blueprint(api_bp)

    return app
