# Setting Up OAuth 2.0 for Gmail Integration

This guide will walk you through the process of setting up OAuth 2.0 credentials for your Live Email Classifier application to properly integrate with Gmail.

## Why OAuth 2.0?

OAuth 2.0 is the industry-standard protocol for authorization. It provides a secure way for your application to access Gmail data without requiring users to share their passwords. Google requires OAuth 2.0 for accessing Gmail APIs.

## Prerequisites

- A Google account
- Your Live Email Classifier application running locally

## Step 1: Create a Google Cloud Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Click on the project dropdown at the top of the page
3. Click on "New Project"
4. Enter a name for your project (e.g., "Live Email Classifier")
5. Click "Create"
6. Wait for the project to be created and then select it from the project dropdown

## Step 2: Enable the Gmail API

1. In the Google Cloud Console, navigate to "APIs & Services" > "Library" from the left sidebar
2. Search for "Gmail API"
3. Click on the Gmail API result
4. Click "Enable" to enable the API for your project

## Step 3: Configure OAuth Consent Screen

1. In the Google Cloud Console, navigate to "APIs & Services" > "OAuth consent screen" from the left sidebar
2. Select "External" as the user type (unless you have a Google Workspace account)
3. Click "Create"
4. Fill in the required information:
   - App name: "Live Email Classifier"
   - User support email: Your email address
   - Developer contact information: Your email address
5. Click "Save and Continue"
6. On the Scopes page, click "Add or Remove Scopes"
7. Add the following scopes:
   - `https://www.googleapis.com/auth/gmail.readonly` (to read emails)
   - `https://www.googleapis.com/auth/gmail.labels` (to manage labels)
   - `https://www.googleapis.com/auth/gmail.metadata` (to access email metadata)
   - `https://www.googleapis.com/auth/userinfo.email` (to get user email address)
8. Click "Save and Continue"
9. On the Test Users page, click "Add Users"
10. Add your email address as a test user
11. Click "Save and Continue"
12. Review your settings and click "Back to Dashboard"

## Step 4: Create OAuth 2.0 Client ID

1. In the Google Cloud Console, navigate to "APIs & Services" > "Credentials" from the left sidebar
2. Click "Create Credentials" and select "OAuth client ID"
3. Select "Web application" as the application type
4. Enter a name for your client (e.g., "Live Email Classifier Web Client")
5. Under "Authorized JavaScript origins", add:
   - `http://localhost:8080` (for local development)
6. Under "Authorized redirect URIs", add:
   - `http://localhost:8080/oauth2callback` (must match the redirect URI in your application)
7. Click "Create"
8. A popup will appear with your client ID and client secret. Click "Download JSON"

## Step 5: Set Up Your Application

1. Rename the downloaded JSON file to `client_secret.json`
2. Move the `client_secret.json` file to the root directory of your Live Email Classifier project
3. Make sure your application's redirect URI matches what you configured in the Google Cloud Console:
   ```python
   app.config['OAUTH_REDIRECT_URI'] = 'http://localhost:8080/oauth2callback'
   ```

## Step 6: Test Your OAuth Integration

1. Run your application:
   ```
   python app.py
   ```
2. Open your browser and navigate to `http://localhost:8080`
3. Click on "Connect Gmail" in the UI
4. You should be redirected to Google's OAuth consent screen
5. Grant permission to your application
6. You should be redirected back to your application with successful authentication

## Troubleshooting

### Error: redirect_uri_mismatch

This error occurs when the redirect URI in your application doesn't match what you configured in the Google Cloud Console.

**Solution:**
- Make sure the redirect URI in your application exactly matches what you configured in the Google Cloud Console
- Check for any typos, extra slashes, or missing protocol (http vs https)
- The redirect URI is case-sensitive

### Error: invalid_client

This error occurs when there's an issue with your client credentials.

**Solution:**
- Make sure your `client_secret.json` file is correctly placed in the root directory of your project
- Check that the file is properly formatted and not corrupted
- Try downloading the credentials file again from the Google Cloud Console

### Error: access_denied

This error occurs when the user denies permission or the scopes requested are not approved in the OAuth consent screen.

**Solution:**
- Make sure all required scopes are added to the OAuth consent screen
- Check that your application is requesting the correct scopes
- Make sure the user is granting permission to all requested scopes

## Security Considerations

- Keep your `client_secret.json` file secure and never commit it to public repositories
- Use environment variables or a secure configuration system for production deployments
- Implement proper token storage and refresh mechanisms
- Follow the principle of least privilege by only requesting the scopes your application needs

## Additional Resources

- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Gmail API Documentation](https://developers.google.com/gmail/api/guides)
- [Google Cloud Console](https://console.cloud.google.com/)
- [OAuth 2.0 Playground](https://developers.google.com/oauthplayground/)
