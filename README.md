# 🚀 Live Email Classifier

A sophisticated Flask web application that automatically classifies emails into categories (Spam, Professional, Casual) using advanced NLP and machine learning techniques, featuring a cyberpunk-themed UI with high animations.

## 📁 Project Structure

```
LIVE EMAIL CLASSIFIER/
├── 📁 app/                     # Main application package
│   ├── __init__.py            # App factory
│   ├── 📁 routes/             # Route handlers
│   │   ├── __init__.py
│   │   ├── main.py           # Main routes (dashboard, emails)
│   │   ├── auth.py           # Authentication routes
│   │   └── api.py            # API endpoints
│   ├── 📁 services/          # Business logic services
│   │   ├── __init__.py
│   │   ├── database.py       # Database operations
│   │   ├── email_analyzer.py # Email analysis service
│   │   ├── email_processor.py # Email processing
│   │   └── google_auth.py    # Google OAuth service
│   ├── 📁 models/            # Data models
│   │   ├── __init__.py
│   │   └── user.py          # User model
│   └── 📁 utils/            # Utility functions
│       ├── __init__.py
│       ├── setup_database.py
│       └── database_setup.sql
├── 📁 classifier/            # ML classifier package
│   ├── __init__.py
│   ├── model.py             # Classifier model
│   ├── preprocessor.py      # Text preprocessing
│   └── training.py          # Model training
├── 📁 config/               # Configuration files
│   ├── config.py           # Configuration management
│   ├── settings.json       # Application settings
│   ├── client_secret.json  # Google OAuth credentials
│   └── client_secret_template.json
├── 📁 static/              # Static web assets
│   ├── 📁 css/            # Stylesheets
│   ├── 📁 js/             # JavaScript files
│   └── 📁 img/            # Images
├── 📁 templates/           # HTML templates
├── 📁 data/               # Data files and models
│   ├── 📁 model/         # Trained ML models
│   ├── training_data.csv # Training dataset
│   └── email_classifier.db # SQLite database
├── 📁 docs/              # Documentation
│   ├── README.md         # This file
│   └── OAUTH_SETUP.md    # OAuth setup guide
├── 📁 tests/             # Test files
├── run_app.py            # Main application entry point
├── app.py               # Legacy main file (for compatibility)
├── main.py              # Utility functions
├── requirements.txt     # Python dependencies
└── run.bat             # Windows batch file to run app
```

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Run the Application
```bash
python run_app.py
```

### 3. Access the Application
Open your browser and navigate to: `http://localhost:8080`

## ✨ Features

- **🎨 Cyberpunk UI**: High-animation, futuristic interface
- **🤖 AI Classification**: Advanced NLP for email categorization
- **🔐 Google OAuth**: Secure Gmail integration
- **📊 Real-time Analytics**: Live statistics and visualizations
- **🎯 Demo Mode**: Test without Gmail credentials
- **📱 Responsive Design**: Works on all devices

## 🔧 Configuration

Configuration files are located in the `config/` directory:

- `config.py`: Main configuration management
- `settings.json`: Application settings
- `client_secret.json`: Google OAuth credentials

## 🗄️ Database

The application uses SQLite for data storage:
- Location: `data/email_classifier.db`
- Setup: Automatic on first run
- Models: User data, email records, statistics

## 🧠 Machine Learning

The classifier uses:
- **Algorithm**: Naive Bayes with TF-IDF vectorization
- **Categories**: Spam, Professional, Casual
- **Training Data**: `data/training_data.csv`
- **Models**: Stored in `data/model/`

## 🔐 Authentication

Supports multiple authentication methods:
1. **Google OAuth 2.0** (Recommended)
2. **Demo Mode** (For testing)

See `docs/OAUTH_SETUP.md` for OAuth setup instructions.

## 🎯 API Endpoints

- `GET /` - Main dashboard
- `POST /api/analyze` - Analyze email content
- `GET /api/emails` - Fetch and classify emails
- `GET /api/stats` - Get classification statistics
- `POST /login` - Start OAuth flow
- `GET /logout` - End session

## 🧪 Testing

Run tests with:
```bash
python -m pytest tests/
```

## 📝 Logs

Application logs are stored in:
- `email_classifier.log`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
1. Check the documentation in `docs/`
2. Review the logs in `email_classifier.log`
3. Enable demo mode for testing
4. Ensure all dependencies are installed

---

**Made with ❤️ for efficient email management**
