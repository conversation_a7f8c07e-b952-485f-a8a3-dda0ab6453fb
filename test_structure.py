"""
Test script to verify the new folder structure works correctly
"""

import sys
import os

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_imports():
    """Test that all imports work correctly with the new structure."""
    print("🧪 Testing new folder structure...")
    
    try:
        # Test config import
        sys.path.append(os.path.join(project_root, 'config'))
        from config import get_config
        print("✅ Config import successful")
        
        # Test app creation
        from app import create_app
        print("✅ App factory import successful")
        
        # Test classifier imports
        from classifier import EmailClassifier
        print("✅ Classifier import successful")
        
        # Test service imports
        from app.services.email_analyzer import EmailAnalyzer
        print("✅ Email analyzer import successful")
        
        # Test route imports
        from app.routes.main import main_bp
        from app.routes.auth import auth_bp
        from app.routes.api import api_bp
        print("✅ Route blueprints import successful")
        
        print("\n🎉 All imports successful! New folder structure is working correctly.")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_app_creation():
    """Test that the app can be created successfully."""
    try:
        from app import create_app
        app = create_app()
        print("✅ Flask app created successfully")
        return True
    except Exception as e:
        print(f"❌ App creation failed: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Live Email Classifier - Structure Test")
    print("=" * 50)
    
    success = test_imports()
    if success:
        success = test_app_creation()
    
    if success:
        print("\n✅ All tests passed! The reorganized structure is ready to use.")
        print("🎯 You can now run the application with: python run_app.py")
    else:
        print("\n❌ Some tests failed. Please check the imports and structure.")
