/**
 * Interactive Email Classification Visualization
 * Provides real-time visualization of email classification process
 */

// Configuration
const visualizationConfig = {
  animationSpeed: 1000,
  particleCount: 50,
  categories: {
    'Spam': {
      color: 'var(--glitch-red)',
      icon: 'fa-bug'
    },
    'HR': {
      color: 'var(--neon-yellow)',
      icon: 'fa-user-tie'
    },
    'Support': {
      color: 'var(--neon-green)',
      icon: 'fa-headset'
    },
    'Casual': {
      color: 'var(--neon-blue)',
      icon: 'fa-comment'
    }
  }
};

// Visualization state
let visualizationActive = false;
let particles = [];
let emailNodes = [];
let categoryNodes = [];

/**
 * Initialize the visualization
 */
function initVisualization() {
  console.log('Initializing email classification visualization...');

  const container = document.getElementById('visualization-container');
  if (!container) return;

  // Clear container
  container.innerHTML = '';

  // Create canvas
  const canvas = document.createElement('canvas');
  canvas.id = 'visualization-canvas';
  canvas.width = container.clientWidth;
  canvas.height = container.clientHeight;
  container.appendChild(canvas);

  // Create category nodes
  createCategoryNodes(container);

  // Create email input node
  createEmailNode(container);

  // Initialize particles
  initParticles();

  // Start animation
  visualizationActive = true;
  animateVisualization();

  // Add resize listener
  window.addEventListener('resize', () => {
    if (container) {
      canvas.width = container.clientWidth;
      canvas.height = container.clientHeight;

      // Reposition nodes
      positionNodes();
    }
  });
}

/**
 * Create category nodes
 */
function createCategoryNodes(container) {
  categoryNodes = [];

  // Get categories from config
  const categories = Object.keys(visualizationConfig.categories);

  // Create a node for each category
  categories.forEach((category, index) => {
    const node = document.createElement('div');
    node.className = 'category-node';
    node.innerHTML = `
      <i class="fas ${visualizationConfig.categories[category].icon}"></i>
      <span>${category}</span>
    `;
    node.style.setProperty('--node-color', visualizationConfig.categories[category].color);

    // Position node
    const angle = (index / categories.length) * Math.PI * 2;
    const radius = Math.min(container.clientWidth, container.clientHeight) * 0.4;
    const x = container.clientWidth / 2 + Math.cos(angle) * radius;
    const y = container.clientHeight / 2 + Math.sin(angle) * radius;

    node.style.left = `${x}px`;
    node.style.top = `${y}px`;

    container.appendChild(node);

    // Add to category nodes
    categoryNodes.push({
      element: node,
      category: category,
      x: x,
      y: y,
      color: visualizationConfig.categories[category].color
    });
  });
}

/**
 * Create email input node
 */
function createEmailNode(container) {
  const node = document.createElement('div');
  node.className = 'email-node';
  node.innerHTML = `
    <i class="fas fa-envelope"></i>
    <span>Email Input</span>
  `;

  // Position node at the center
  const x = container.clientWidth / 2;
  const y = container.clientHeight / 2;

  node.style.left = `${x}px`;
  node.style.top = `${y}px`;

  container.appendChild(node);

  // Add to email nodes
  emailNodes = [{
    element: node,
    x: x,
    y: y
  }];
}

/**
 * Initialize particles
 */
function initParticles() {
  particles = [];

  for (let i = 0; i < visualizationConfig.particleCount; i++) {
    particles.push({
      x: 0,
      y: 0,
      targetX: 0,
      targetY: 0,
      color: 'rgba(255, 255, 255, 0.7)',
      size: Math.random() * 3 + 1,
      speed: Math.random() * 0.5 + 0.2,
      progress: 0,
      active: false
    });
  }
}

/**
 * Animate visualization
 */
function animateVisualization() {
  if (!visualizationActive) return;

  const canvas = document.getElementById('visualization-canvas');
  if (!canvas) return;

  const ctx = canvas.getContext('2d');

  // Clear canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // Draw connections
  drawConnections(ctx);

  // Draw particles
  drawParticles(ctx);

  // Request next frame
  requestAnimationFrame(animateVisualization);
}

/**
 * Draw connections between nodes
 */
function drawConnections(ctx) {
  // Draw connections from email node to category nodes
  if (emailNodes.length > 0 && categoryNodes.length > 0) {
    const emailNode = emailNodes[0];

    categoryNodes.forEach(categoryNode => {
      ctx.beginPath();
      ctx.moveTo(emailNode.x, emailNode.y);
      ctx.lineTo(categoryNode.x, categoryNode.y);
      ctx.strokeStyle = `${categoryNode.color}40`; // 25% opacity
      ctx.lineWidth = 1;
      ctx.stroke();
    });
  }
}

/**
 * Draw particles
 */
function drawParticles(ctx) {
  particles.forEach(particle => {
    if (particle.active) {
      // Update position
      particle.progress += particle.speed;

      if (particle.progress >= 1) {
        particle.active = false;
        return;
      }

      // Calculate current position
      const x = particle.x + (particle.targetX - particle.x) * particle.progress;
      const y = particle.y + (particle.targetY - particle.y) * particle.progress;

      // Draw particle
      ctx.beginPath();
      ctx.arc(x, y, particle.size, 0, Math.PI * 2);
      ctx.fillStyle = particle.color;
      ctx.fill();
    }
  });
}

/**
 * Reposition nodes on resize
 */
function positionNodes() {
  const container = document.getElementById('visualization-container');
  if (!container) return;

  // Reposition category nodes
  categoryNodes.forEach((node, index) => {
    const angle = (index / categoryNodes.length) * Math.PI * 2;
    const radius = Math.min(container.clientWidth, container.clientHeight) * 0.4;
    const x = container.clientWidth / 2 + Math.cos(angle) * radius;
    const y = container.clientHeight / 2 + Math.sin(angle) * radius;

    node.x = x;
    node.y = y;
    node.element.style.left = `${x}px`;
    node.element.style.top = `${y}px`;
  });

  // Reposition email node
  if (emailNodes.length > 0) {
    const node = emailNodes[0];
    const x = container.clientWidth / 2;
    const y = container.clientHeight / 2;

    node.x = x;
    node.y = y;
    node.element.style.left = `${x}px`;
    node.element.style.top = `${y}px`;
  }
}

/**
 * Visualize classification of an email
 */
function visualizeClassification(category, confidence) {
  // Find target category node
  const targetNode = categoryNodes.find(node => node.category === category);
  if (!targetNode) return;

  // Get source node
  const sourceNode = emailNodes[0];
  if (!sourceNode) return;

  // Activate particles
  const particleCount = Math.floor(visualizationConfig.particleCount * confidence);
  let activatedCount = 0;

  particles.forEach(particle => {
    if (!particle.active && activatedCount < particleCount) {
      particle.x = sourceNode.x;
      particle.y = sourceNode.y;
      particle.targetX = targetNode.x;
      particle.targetY = targetNode.y;
      particle.color = targetNode.color;
      particle.progress = 0;
      particle.active = true;
      activatedCount++;
    }
  });

  // Highlight target category
  categoryNodes.forEach(node => {
    node.element.classList.remove('active');
  });
  targetNode.element.classList.add('active');
}

/**
 * Reset the visualization
 */
function resetVisualization() {
  // Reset particles
  particles.forEach(particle => {
    particle.active = false;
  });

  // Remove active class from category nodes
  categoryNodes.forEach(node => {
    node.element.classList.remove('active');
  });
}

// Initialize visualization when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Initialize visualization
  initVisualization();

  // Connect to analyze form
  const analyzeForm = document.getElementById('analyze-form');
  if (analyzeForm) {
    analyzeForm.addEventListener('submit', event => {
      // The form submission is already handled in script.js
      // We just need to hook into the result display

      // The visualization will be triggered by the displayAnalysisResult function
    });
  }

  // Connect reset button
  const resetButton = document.getElementById('reset-visualization');
  if (resetButton) {
    resetButton.addEventListener('click', () => {
      resetVisualization();
    });
  }
});

// Export functions for use in other scripts
window.visualizeClassification = visualizeClassification;
