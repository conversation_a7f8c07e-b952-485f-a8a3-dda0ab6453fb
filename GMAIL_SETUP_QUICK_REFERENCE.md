# 🚀 Gmail Integration - Quick Setup Reference

## ⚡ Quick Start (5 Minutes)

### **1. Google Cloud Console Setup**
🌐 **URL**: https://console.cloud.google.com/

**Create Project:**
- Click "New Project"
- Name: "Live Email Classifier"
- Click "Create"

### **2. Enable Gmail API**
📍 **Path**: APIs & Services → Library
- Search: "Gmail API"
- Click "Enable"

### **3. OAuth Consent Screen**
📍 **Path**: APIs & Services → OAuth consent screen
- User Type: **External**
- App Name: **Live Email Classifier**
- Support Email: **Your email**
- Developer Contact: **Your email**

**Required Scopes:**
```
https://www.googleapis.com/auth/gmail.readonly
https://www.googleapis.com/auth/gmail.labels
https://www.googleapis.com/auth/gmail.metadata
https://www.googleapis.com/auth/userinfo.email
```

**Test Users:**
- Add your email address

### **4. Create OAuth Client**
📍 **Path**: APIs & Services → Credentials
- Click "Create Credentials" → "OAuth client ID"
- Type: **Web application**
- Name: **Live Email Classifier Web Client**

**Authorized Origins:**
```
http://localhost:8080
```

**Authorized Redirect URIs:**
```
http://localhost:8080/oauth2callback
```

### **5. Download & Install**
- Click "Download JSON"
- Rename to: `client_secret.json`
- Move to: `config/client_secret.json`
- Restart application

## 🔧 Setup Assistant

Run the interactive setup assistant:
```bash
python setup_gmail_oauth.py
```

## ✅ Verification Steps

### **Check Configuration:**
```bash
# Verify file exists
ls -la config/client_secret.json

# Check content (should not contain "YOUR_CLIENT_ID")
cat config/client_secret.json
```

### **Test OAuth Flow:**
1. Open: http://localhost:8080
2. Click "Google OAuth"
3. Grant permissions
4. Should redirect back successfully

## 🎯 Current Application Settings

**Redirect URI:** `http://localhost:8080/oauth2callback`
**Port:** `8080`
**Config Location:** `config/client_secret.json`

## 🚨 Common Issues & Solutions

### **❌ redirect_uri_mismatch**
**Problem:** Redirect URI doesn't match
**Solution:** Ensure Google Console has: `http://localhost:8080/oauth2callback`

### **❌ invalid_client**
**Problem:** Wrong credentials file
**Solution:** Re-download `client_secret.json` from Google Console

### **❌ access_denied**
**Problem:** Missing scopes or permissions
**Solution:** Check OAuth consent screen has all required scopes

### **❌ Template values still present**
**Problem:** File contains "YOUR_CLIENT_ID"
**Solution:** Replace with real credentials from Google Console

## 🎉 Success Indicators

✅ **File Check:**
```json
{
  "web": {
    "client_id": "*************.apps.googleusercontent.com",
    "client_secret": "GOCSPX-real_secret_here",
    "redirect_uris": ["http://localhost:8080/oauth2callback"]
  }
}
```

✅ **Application Behavior:**
- "Google OAuth" button works
- Redirects to Google login
- Returns to app after permission grant
- Shows real Gmail data instead of demo

## 📞 Support

**Documentation:** `docs/OAUTH_SETUP.md`
**Setup Assistant:** `python setup_gmail_oauth.py`
**Google Docs:** https://developers.google.com/gmail/api/guides

---

**🎊 Once configured, your Live Email Classifier will have full Gmail integration!**
