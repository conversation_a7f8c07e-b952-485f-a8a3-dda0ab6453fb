"""
Live Email Classifier - Main Application Entry Point
A Flask web application that classifies emails into categories using machine learning.
"""

import os
import sys
import logging

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def initialize_classifier():
    """Initialize the email classifier."""
    try:
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
        from config import get_config
        from classifier import EmailClassifier, train_model

        # Load configuration
        config = get_config()

        # Initialize classifier
        classifier = EmailClassifier(
            model_path=config['classifier']['model_path'],
            vectorizer_path=config['classifier']['vectorizer_path'],
            categories=config['classifier']['categories']
        )

        # Train model if it doesn't exist
        if not os.path.exists(config['classifier']['model_path']):
            logging.info("Training new model...")

            # Create sample training data if it doesn't exist
            if not os.path.exists(config['classifier']['training_data']):
                from main import create_sample_training_data
                create_sample_training_data()

            train_result = train_model(
                training_data_path=config['classifier']['training_data'],
                model_path=config['classifier']['model_path'],
                vectorizer_path=config['classifier']['vectorizer_path'],
                categories=config['classifier']['categories']
            )

            if not train_result['success']:
                logging.error(f"Model training failed: {train_result.get('error', 'Unknown error')}")
                return False

        # Load the model
        if not classifier.load_model():
            logging.error("Failed to load model")
            return False

        logging.info("Classifier initialized successfully")
        return True

    except Exception as e:
        logging.error(f"Error initializing classifier: {str(e)}")
        return False

def create_app():
    """Create and configure the Flask application."""
    from app import create_app

    # Create the Flask app
    app = create_app()

    # Initialize classifier
    initialize_classifier()

    return app

if __name__ == '__main__':
    # Create data directory if it doesn't exist
    os.makedirs('data/model', exist_ok=True)

    # Create and run the Flask app
    app = create_app()

    # Run the Flask app
    app.run(debug=True, host='localhost', port=8080)
