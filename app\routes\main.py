"""
Main routes for the Live Email Classifier application
"""

from flask import Blueprint, render_template, session
import logging

main_bp = Blueprint('main', __name__)
logger = logging.getLogger(__name__)

@main_bp.route('/')
def index():
    """Render the hacker-themed dashboard page."""
    from app.services.google_auth import get_gmail_service
    
    gmail_service = None
    user_info = None
    
    # Check if already authenticated with Gmail
    if 'credentials' in session:
        if gmail_service is None:
            gmail_service, user_info = get_gmail_service()

    return render_template('hacker_demo.html', user_info=user_info)

@main_bp.route('/emails')
def emails():
    """Render the emails page."""
    return render_template('emails.html')

@main_bp.route('/classify')
def classify():
    """Render the classify page."""
    return render_template('classify.html')
