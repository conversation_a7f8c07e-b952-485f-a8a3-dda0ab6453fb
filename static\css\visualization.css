/* Visualization Styles for Live Email Classifier
   Interactive visualization of email classification */

/* Visualization Container */
.visualization-container {
  position: relative;
  width: 100%;
  height: 300px;
  margin: 20px 0;
  overflow: hidden;
  border-radius: 5px;
  background-color: var(--bg-dark);
  border: 1px solid var(--neon-blue);
  box-shadow: 0 0 10px rgba(0, 102, 255, 0.2);
}

/* Visualization Canvas */
#visualization-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* Node Styles */
.email-node,
.category-node {
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  cursor: pointer;
  user-select: none;
}

/* Email Node */
.email-node {
  background-color: var(--bg-dark);
  border: 2px solid var(--neon-blue);
  color: var(--neon-blue);
  box-shadow: 0 0 10px rgba(0, 102, 255, 0.5);
}

.email-node i {
  font-size: 1.5rem;
  margin-bottom: 5px;
}

.email-node span {
  font-size: 0.7rem;
  font-family: var(--font-mono);
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Category Node */
.category-node {
  background-color: var(--bg-dark);
  border: 2px solid var(--node-color, var(--neon-green));
  color: var(--node-color, var(--neon-green));
  box-shadow: 0 0 10px var(--node-color, rgba(0, 255, 0, 0.5));
}

.category-node i {
  font-size: 1.5rem;
  margin-bottom: 5px;
}

.category-node span {
  font-size: 0.7rem;
  font-family: var(--font-mono);
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Active Category Node */
.category-node.active {
  transform: translate(-50%, -50%) scale(1.2);
  box-shadow: 0 0 20px var(--node-color, rgba(0, 255, 0, 0.8));
}

/* Hover Effects */
.email-node:hover,
.category-node:hover {
  transform: translate(-50%, -50%) scale(1.1);
}

.category-node.active:hover {
  transform: translate(-50%, -50%) scale(1.3);
}

/* Visualization Title */
.visualization-title {
  text-align: center;
  margin-bottom: 10px;
  font-family: var(--font-mono);
  color: var(--text-primary);
  text-transform: uppercase;
  letter-spacing: 2px;
}

/* Visualization Controls */
.visualization-controls {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.visualization-controls button {
  margin: 0 5px;
  padding: 5px 10px;
  background-color: var(--bg-dark);
  border: 1px solid var(--neon-blue);
  color: var(--neon-blue);
  font-family: var(--font-mono);
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.visualization-controls button:hover {
  background-color: var(--neon-blue);
  color: var(--bg-dark);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .visualization-container {
    height: 250px;
  }
  
  .email-node,
  .category-node {
    width: 50px;
    height: 50px;
  }
  
  .email-node i,
  .category-node i {
    font-size: 1.2rem;
  }
  
  .email-node span,
  .category-node span {
    font-size: 0.6rem;
  }
}

@media (max-width: 480px) {
  .visualization-container {
    height: 200px;
  }
  
  .email-node,
  .category-node {
    width: 40px;
    height: 40px;
  }
  
  .email-node i,
  .category-node i {
    font-size: 1rem;
    margin-bottom: 2px;
  }
  
  .email-node span,
  .category-node span {
    font-size: 0.5rem;
  }
}
