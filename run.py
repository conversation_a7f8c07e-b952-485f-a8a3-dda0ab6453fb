"""
Live Email Classifier with Gmail Integration
Main entry point for the application - Presentation Version
"""

import os
import webbrowser
import threading
import time
import logging
import subprocess
import sys
from app import app

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('email_classifier.log')
    ]
)

logger = logging.getLogger(__name__)

def open_browser():
    """Open the browser to the application URL after a short delay."""
    time.sleep(2)  # Wait for the server to start
    url = "http://127.0.0.1:5000"
    webbrowser.open(url)
    logger.info(f"Opening browser at {url}")

def check_prerequisites():
    """Check if all prerequisites are met."""
    # Create necessary directories
    os.makedirs('data/model', exist_ok=True)

    # Check if database setup script exists and run it
    if os.path.exists('setup_database.py'):
        try:
            print("Setting up database...")
            subprocess.check_call([sys.executable, "setup_database.py"])
            print("Database setup complete.")
        except Exception as e:
            logger.warning(f"Database setup failed: {str(e)}")
            print("Database setup failed, but continuing anyway.")

    # Check if client_secret.json exists with valid content
    if not os.path.exists('client_secret.json'):
        logger.warning("client_secret.json not found. Creating placeholder...")
        with open('client_secret.json', 'w') as f:
            f.write('''{
  "web": ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
}''')
        print("\n" + "="*80)
        print("PRESENTATION MODE: Using placeholder client_secret.json")
        print("The application will run in demo mode for presentation purposes.")
        print("You can demonstrate the email classification functionality using sample emails.")
        print("="*80 + "\n")

    return True

if __name__ == "__main__":
    print("""
    ╔═══════════════════════════════════════════════════════════════════╗
    ║                                                                   ║
    ║   LIVE EMAIL CLASSIFIER - CYBERPUNK EDITION                       ║
    ║   with Gmail Integration                                          ║
    ║                                                                   ║
    ║   PRESENTATION MODE                                               ║
    ║   Analyzing emails as spam, professional, or casual               ║
    ║                                                                   ║
    ╚═══════════════════════════════════════════════════════════════════╝
    """)

    # Check prerequisites
    check_prerequisites()

    # Start browser in a separate thread
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()

    print("\nStarting the application...")
    print("The web interface will open automatically in your browser.")
    print("If it doesn't open, navigate to: http://localhost:8080")
    print("\nPress Ctrl+C to stop the server when you're done.")
    print("="*80 + "\n")

    # Run the Flask application
    try:
        app.run(debug=True, host='localhost', port=8080)
    except KeyboardInterrupt:
        print("\nShutting down the application...")
    except Exception as e:
        logger.error(f"Error running application: {str(e)}")
        print(f"\nError: {str(e)}")
    finally:
        print("\nThank you for using Live Email Classifier!")
