"""
Advanced NLP Engine for Live Email Classifier
Comprehensive natural language processing for automated email workflow
"""

import re
import json
import logging
import datetime
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

import nltk
import spacy
from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
from textblob import TextBlob
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

# Download required NLTK data
try:
    nltk.download('vader_lexicon', quiet=True)
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
    nltk.download('wordnet', quiet=True)
    nltk.download('averaged_perceptron_tagger', quiet=True)
    nltk.download('maxent_ne_chunker', quiet=True)
    nltk.download('words', quiet=True)
except:
    pass

from nltk.sentiment import SentimentIntensityAnalyzer
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.corpus import stopwords
from nltk.stem import WordNetLemmatizer
from nltk.chunk import ne_chunk
from nltk.tag import pos_tag

logger = logging.getLogger(__name__)

class Priority(Enum):
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class EmailCategory(Enum):
    SPAM = "spam"
    PROFESSIONAL = "professional"
    CASUAL = "casual"
    URGENT = "urgent"
    NEWSLETTER = "newsletter"
    PROMOTIONAL = "promotional"
    SUPPORT = "support"
    HR = "hr"
    FINANCE = "finance"
    MEETING = "meeting"

@dataclass
class NLPAnalysis:
    """Comprehensive NLP analysis result."""
    category: EmailCategory
    priority: Priority
    confidence: float
    sentiment: Dict[str, float]
    keywords: List[str]
    entities: List[Dict[str, Any]]
    intent: str
    urgency_score: float
    spam_probability: float
    language: str
    reading_time: int
    summary: str
    action_items: List[str]
    suggested_response: Optional[str]
    auto_labels: List[str]
    workflow_actions: List[Dict[str, Any]]

class AdvancedNLPEngine:
    """Advanced NLP Engine for comprehensive email analysis."""
    
    def __init__(self):
        """Initialize the NLP engine with all models."""
        self.logger = logging.getLogger(__name__)
        self.logger.info("Initializing Advanced NLP Engine...")
        
        # Initialize components
        self._init_nltk_components()
        self._init_spacy_model()
        self._init_transformers()
        self._init_custom_models()
        
        # Load configuration
        self._load_patterns()
        self._load_workflows()
        
        self.logger.info("Advanced NLP Engine initialized successfully!")
    
    def _init_nltk_components(self):
        """Initialize NLTK components."""
        try:
            self.sentiment_analyzer = SentimentIntensityAnalyzer()
            self.lemmatizer = WordNetLemmatizer()
            self.stop_words = set(stopwords.words('english'))
            self.logger.info("NLTK components initialized")
        except Exception as e:
            self.logger.error(f"Error initializing NLTK: {e}")
            # Fallback initialization
            self.sentiment_analyzer = None
            self.lemmatizer = None
            self.stop_words = set()
    
    def _init_spacy_model(self):
        """Initialize spaCy model for NER and advanced processing."""
        try:
            # Try to load English model
            self.nlp = spacy.load("en_core_web_sm")
            self.logger.info("spaCy model loaded successfully")
        except OSError:
            self.logger.warning("spaCy English model not found, using basic processing")
            self.nlp = None
    
    def _init_transformers(self):
        """Initialize transformer models for advanced classification."""
        try:
            # Sentiment analysis
            self.sentiment_pipeline = pipeline(
                "sentiment-analysis",
                model="cardiffnlp/twitter-roberta-base-sentiment-latest",
                return_all_scores=True
            )
            
            # Text classification
            self.classification_pipeline = pipeline(
                "text-classification",
                model="microsoft/DialoGPT-medium"
            )
            
            # Summarization
            self.summarization_pipeline = pipeline(
                "summarization",
                model="facebook/bart-large-cnn"
            )
            
            self.logger.info("Transformer models initialized")
        except Exception as e:
            self.logger.warning(f"Could not load transformer models: {e}")
            self.sentiment_pipeline = None
            self.classification_pipeline = None
            self.summarization_pipeline = None
    
    def _init_custom_models(self):
        """Initialize custom models and vectorizers."""
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=5000,
            stop_words='english',
            ngram_range=(1, 3)
        )
        
        # Priority keywords
        self.priority_keywords = {
            Priority.CRITICAL: [
                'urgent', 'emergency', 'critical', 'asap', 'immediately',
                'deadline', 'crisis', 'alert', 'warning', 'security breach'
            ],
            Priority.HIGH: [
                'important', 'priority', 'soon', 'today', 'tomorrow',
                'meeting', 'interview', 'deadline', 'review'
            ],
            Priority.MEDIUM: [
                'update', 'information', 'reminder', 'follow up',
                'schedule', 'planning', 'discussion'
            ],
            Priority.LOW: [
                'fyi', 'newsletter', 'announcement', 'social',
                'casual', 'optional', 'when convenient'
            ]
        }
        
        # Spam indicators
        self.spam_indicators = [
            'free', 'win', 'winner', 'congratulations', 'prize',
            'click here', 'act now', 'limited time', 'offer expires',
            'make money', 'work from home', 'guaranteed', 'risk free',
            'no obligation', 'credit card', 'loan', 'debt'
        ]
    
    def _load_patterns(self):
        """Load regex patterns for email analysis."""
        self.patterns = {
            'email': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            'phone': re.compile(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b'),
            'url': re.compile(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'),
            'date': re.compile(r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b'),
            'time': re.compile(r'\b\d{1,2}:\d{2}(?:\s?[AaPp][Mm])?\b'),
            'money': re.compile(r'\$\d+(?:,\d{3})*(?:\.\d{2})?'),
            'meeting': re.compile(r'\b(?:meeting|call|conference|zoom|teams)\b', re.IGNORECASE),
            'deadline': re.compile(r'\b(?:deadline|due|expires?|ends?)\b', re.IGNORECASE)
        }
    
    def _load_workflows(self):
        """Load automated workflow configurations."""
        self.workflows = {
            'spam': {
                'actions': ['move_to_spam', 'mark_as_read'],
                'auto_response': False,
                'priority': Priority.LOW
            },
            'urgent': {
                'actions': ['mark_important', 'notify_user', 'add_to_calendar'],
                'auto_response': True,
                'priority': Priority.CRITICAL
            },
            'meeting': {
                'actions': ['extract_calendar_info', 'add_to_calendar', 'set_reminder'],
                'auto_response': False,
                'priority': Priority.HIGH
            },
            'support': {
                'actions': ['create_ticket', 'auto_categorize', 'assign_agent'],
                'auto_response': True,
                'priority': Priority.MEDIUM
            }
        }
    
    def analyze_email(self, email_data: Dict[str, Any]) -> NLPAnalysis:
        """
        Perform comprehensive NLP analysis on an email.
        
        Args:
            email_data: Dictionary containing email information
            
        Returns:
            NLPAnalysis: Comprehensive analysis results
        """
        try:
            # Extract text content
            subject = email_data.get('subject', '')
            body = email_data.get('body', '')
            sender = email_data.get('sender', '')
            
            # Combine text for analysis
            full_text = f"{subject} {body}"
            
            # Perform analysis
            category = self._classify_email(full_text, sender)
            priority = self._determine_priority(full_text)
            confidence = self._calculate_confidence(full_text, category)
            sentiment = self._analyze_sentiment(full_text)
            keywords = self._extract_keywords(full_text)
            entities = self._extract_entities(full_text)
            intent = self._detect_intent(full_text)
            urgency_score = self._calculate_urgency(full_text)
            spam_probability = self._calculate_spam_probability(full_text, sender)
            language = self._detect_language(full_text)
            reading_time = self._estimate_reading_time(full_text)
            summary = self._generate_summary(full_text)
            action_items = self._extract_action_items(full_text)
            suggested_response = self._generate_suggested_response(full_text, category)
            auto_labels = self._generate_auto_labels(full_text, category, entities)
            workflow_actions = self._determine_workflow_actions(category, priority, intent)
            
            return NLPAnalysis(
                category=category,
                priority=priority,
                confidence=confidence,
                sentiment=sentiment,
                keywords=keywords,
                entities=entities,
                intent=intent,
                urgency_score=urgency_score,
                spam_probability=spam_probability,
                language=language,
                reading_time=reading_time,
                summary=summary,
                action_items=action_items,
                suggested_response=suggested_response,
                auto_labels=auto_labels,
                workflow_actions=workflow_actions
            )
            
        except Exception as e:
            self.logger.error(f"Error in email analysis: {e}")
            # Return basic analysis on error
            return self._create_fallback_analysis(email_data)
