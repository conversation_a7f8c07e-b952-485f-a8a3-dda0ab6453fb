/**
 * Matrix Rain Effect for Hacker Theme
 * Creates a canvas with falling matrix code
 */

document.addEventListener('DOMContentLoaded', () => {
    // Create canvas element
    const canvas = document.createElement('canvas');
    canvas.className = 'matrix-rain';
    document.body.appendChild(canvas);
    
    // Initialize matrix rain
    initMatrixRain(canvas);
});

function initMatrixRain(canvas) {
    const ctx = canvas.getContext('2d');
    
    // Set canvas dimensions
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    
    // Characters to display (can be customized)
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789$+-*/=%"\'#&_(),.;:?!\\|{}<>[]^~';
    
    // Column settings
    const fontSize = 14;
    const columns = Math.floor(canvas.width / fontSize);
    
    // Drops - one per column
    const drops = [];
    for (let i = 0; i < columns; i++) {
        drops[i] = Math.floor(Math.random() * -canvas.height);
    }
    
    // Draw the matrix rain
    function draw() {
        // Black semi-transparent background to create fade effect
        ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Green text
        ctx.fillStyle = '#00ff00';
        ctx.font = fontSize + 'px monospace';
        
        // Draw characters
        for (let i = 0; i < drops.length; i++) {
            // Random character
            const char = chars[Math.floor(Math.random() * chars.length)];
            
            // Draw character
            ctx.fillText(char, i * fontSize, drops[i] * fontSize);
            
            // Reset drop when it reaches bottom or randomly
            if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                drops[i] = 0;
            }
            
            // Move drop down
            drops[i]++;
        }
    }
    
    // Handle window resize
    window.addEventListener('resize', () => {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        
        // Recalculate columns
        const newColumns = Math.floor(canvas.width / fontSize);
        
        // Adjust drops array
        if (newColumns > drops.length) {
            // Add new drops
            for (let i = drops.length; i < newColumns; i++) {
                drops[i] = Math.floor(Math.random() * -canvas.height);
            }
        } else if (newColumns < drops.length) {
            // Remove excess drops
            drops.length = newColumns;
        }
    });
    
    // Run animation
    setInterval(draw, 33); // ~30fps
}

/**
 * Terminal typing effect
 * Creates a typewriter effect for elements with class 'terminal-text'
 */
document.addEventListener('DOMContentLoaded', () => {
    const terminalElements = document.querySelectorAll('.terminal-text');
    
    terminalElements.forEach(element => {
        const text = element.textContent;
        element.textContent = '';
        
        // Add cursor
        const cursor = document.createElement('span');
        cursor.className = 'terminal-cursor';
        element.appendChild(cursor);
        
        // Type text
        let i = 0;
        const typeInterval = setInterval(() => {
            if (i < text.length) {
                element.insertBefore(document.createTextNode(text.charAt(i)), cursor);
                i++;
            } else {
                clearInterval(typeInterval);
                
                // Remove cursor after typing is complete (optional)
                // setTimeout(() => {
                //     cursor.remove();
                // }, 2000);
            }
        }, 50); // Adjust typing speed here
    });
});
