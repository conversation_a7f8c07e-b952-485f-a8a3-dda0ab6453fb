/* Cyberpunk Theme for Live Email Classifier
   Main Stylesheet - Cyberpunk 2077 inspired */

:root {
  /* Cyberpunk Color Palette */
  --neon-blue: #00f3ff;
  --neon-pink: #ff00ff;
  --neon-yellow: #f7ff00;
  --neon-green: #00ff66;
  --dark-blue: #0d0221;
  --dark-purple: #190933;
  --cyber-black: #0a0a0a;
  --cyber-dark: #121212;
  --cyber-gray: #1f1f1f;
  --cyber-light: #2a2a2a;
  --glitch-blue: #00ddff;
  --glitch-red: #ff003c;
  
  /* Typography */
  --font-main: 'Raj<PERSON>ni', sans-serif;
  --font-alt: 'Orbitron', sans-serif;
  --font-mono: 'Share Tech Mono', monospace;
  
  /* Effects */
  --glow-blue: 0 0 10px var(--neon-blue), 0 0 20px rgba(0, 243, 255, 0.5);
  --glow-pink: 0 0 10px var(--neon-pink), 0 0 20px rgba(255, 0, 255, 0.5);
  --glow-yellow: 0 0 10px var(--neon-yellow), 0 0 20px rgba(247, 255, 0, 0.5);
  --glow-green: 0 0 10px var(--neon-green), 0 0 20px rgba(0, 255, 102, 0.5);
  
  /* Sizes */
  --header-height: 80px;
  --sidebar-width: 280px;
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-main);
  background-color: var(--cyber-black);
  color: #ffffff;
  line-height: 1.6;
  overflow-x: hidden;
  background-image: 
    linear-gradient(to bottom, rgba(10, 10, 10, 0.9), rgba(10, 10, 10, 0.95)),
    url('../img/cyber-grid.png');
  background-size: cover;
  background-attachment: fixed;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--cyber-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--neon-blue);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neon-pink);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-alt);
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 1rem;
}

h1 {
  font-size: 2.5rem;
  background: linear-gradient(to right, var(--neon-blue), var(--neon-pink));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 0 8px rgba(0, 243, 255, 0.5);
}

h2 {
  font-size: 2rem;
  color: var(--neon-blue);
}

h3 {
  font-size: 1.5rem;
  color: var(--neon-yellow);
}

p {
  margin-bottom: 1rem;
}

a {
  color: var(--neon-blue);
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
}

a:hover {
  color: var(--neon-pink);
  text-shadow: var(--glow-pink);
}

a::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  bottom: -4px;
  left: 0;
  background: linear-gradient(to right, var(--neon-blue), var(--neon-pink));
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s ease;
}

a:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

/* Layout */
.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

.header {
  height: var(--header-height);
  background-color: rgba(10, 10, 10, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 2px solid var(--neon-blue);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
}

.logo {
  display: flex;
  align-items: center;
}

.logo img {
  height: 40px;
  margin-right: 10px;
}

.logo-text {
  font-family: var(--font-alt);
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--neon-blue);
  text-shadow: var(--glow-blue);
}

.main-content {
  display: flex;
  min-height: calc(100vh - var(--header-height));
  margin-top: var(--header-height);
}

.sidebar {
  width: var(--sidebar-width);
  background-color: rgba(25, 9, 51, 0.8);
  backdrop-filter: blur(10px);
  border-right: 1px solid var(--neon-pink);
  padding: 2rem 1rem;
  position: fixed;
  height: calc(100vh - var(--header-height));
  overflow-y: auto;
}

.content {
  flex: 1;
  margin-left: var(--sidebar-width);
  padding: 2rem;
}

/* Navigation */
.nav-menu {
  list-style: none;
  margin-top: 2rem;
}

.nav-item {
  margin-bottom: 1rem;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.8rem 1rem;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-family: var(--font-alt);
  font-size: 1.1rem;
}

.nav-link i {
  margin-right: 10px;
  font-size: 1.2rem;
}

.nav-link:hover {
  background-color: rgba(0, 243, 255, 0.1);
  transform: translateX(5px);
}

.nav-link.active {
  background-color: rgba(0, 243, 255, 0.2);
  border-left: 3px solid var(--neon-blue);
}

/* Cards */
.card {
  background-color: var(--cyber-gray);
  border-radius: 8px;
  border: 1px solid var(--cyber-light);
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, var(--neon-blue), var(--neon-pink));
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid var(--cyber-light);
  padding-bottom: 1rem;
}

.card-title {
  margin-bottom: 0;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 0.8rem 1.5rem;
  border-radius: 4px;
  font-family: var(--font-alt);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border: none;
  outline: none;
}

.btn::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
  transform: rotate(30deg);
  transition: transform 0.6s ease;
  opacity: 0;
}

.btn:hover::after {
  transform: rotate(30deg) translate(100%, 100%);
  opacity: 1;
}

.btn-primary {
  background-color: var(--neon-blue);
  color: var(--cyber-black);
  box-shadow: 0 0 15px rgba(0, 243, 255, 0.5);
}

.btn-primary:hover {
  background-color: var(--glitch-blue);
  transform: translateY(-3px);
  box-shadow: 0 0 20px rgba(0, 243, 255, 0.7);
}

.btn-secondary {
  background-color: var(--neon-pink);
  color: var(--cyber-black);
  box-shadow: 0 0 15px rgba(255, 0, 255, 0.5);
}

.btn-secondary:hover {
  background-color: var(--glitch-red);
  transform: translateY(-3px);
  box-shadow: 0 0 20px rgba(255, 0, 255, 0.7);
}

.btn-outline {
  background-color: transparent;
  border: 2px solid var(--neon-blue);
  color: var(--neon-blue);
}

.btn-outline:hover {
  background-color: rgba(0, 243, 255, 0.1);
  box-shadow: var(--glow-blue);
}
