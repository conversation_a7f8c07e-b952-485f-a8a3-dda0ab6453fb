"""
API routes for the Live Email Classifier application
"""

from flask import Blueprint, request, jsonify, session
import logging

api_bp = Blueprint('api', __name__, url_prefix='/api')
logger = logging.getLogger(__name__)

@api_bp.route('/analyze', methods=['POST'])
def analyze_email():
    """Analyze a single email."""
    try:
        from app.services.email_analyzer import EmailAnalyzer

        data = request.get_json()
        email_text = data.get('email_text', '')

        if not email_text:
            return jsonify({'error': 'No email text provided'}), 400

        # Create email data
        email_data = {
            'subject': data.get('subject', ''),
            'body': email_text,
            'sender': data.get('sender', '')
        }

        # Initialize email analyzer
        email_analyzer = EmailAnalyzer()

        # Analyze the email
        analysis = email_analyzer.analyze_email(email_data)

        # Return the analysis
        return jsonify({
            'category': analysis['category'],
            'confidence': analysis['confidence'],
            'keywords': analysis['keywords'],
            'probabilities': analysis.get('probabilities', {})
        })

    except Exception as e:
        logger.error(f"Error analyzing email: {str(e)}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/stats')
def get_stats():
    """Get statistics for the dashboard."""
    try:
        from app.services.database import get_statistics

        # Get user ID from session
        user_id = session.get('user_id')

        # Get statistics from database
        stats = get_statistics(user_id)

        # Format statistics for frontend
        formatted_stats = {
            'totalEmails': stats.get('total_emails', 0),
            'classifiedEmails': stats.get('classified_emails', 0),
            'spamEmails': stats.get('spam_emails', 0),
            'hrEmails': stats.get('hr_emails', 0),
            'supportEmails': stats.get('support_emails', 0),
            'casualEmails': stats.get('casual_emails', 0),
            'accuracy': stats.get('accuracy', 0) * 100  # Convert to percentage
        }

        return jsonify(formatted_stats)
    except Exception as e:
        logger.error(f"Error getting statistics: {str(e)}")

        # Return default statistics if there's an error
        default_stats = {
            'totalEmails': 0,
            'classifiedEmails': 0,
            'spamEmails': 0,
            'hrEmails': 0,
            'supportEmails': 0,
            'casualEmails': 0,
            'accuracy': 0
        }

        return jsonify(default_stats)

@api_bp.route('/emails')
def get_email_list():
    """Get list of emails from Gmail and analyze them."""
    try:
        from app.services.google_auth import get_gmail_service, fetch_emails
        from app.services.email_analyzer import EmailAnalyzer
        from app.services.database import save_email, get_emails, update_statistics

        # Check if in demo mode
        if 'demo_mode' in session and session['demo_mode']:
            # Return enhanced demo data
            demo_emails = [
                {
                    'id': 'demo-email-1',
                    'subject': '🚨 URGENT: Suspicious Login Detected - Verify Now!',
                    'sender': '<EMAIL>',
                    'date': '2024-05-24 14:32',
                    'snippet': 'ALERT: We detected an unauthorized login attempt from Russia. Click here immediately to secure your account or it will be suspended...',
                    'category': 'spam',
                    'confidence': 0.94,
                    'keywords': ['urgent', 'suspicious', 'verify', 'unauthorized', 'suspended'],
                    'body': 'SECURITY ALERT\n\nWe have detected a suspicious login attempt to your account from an unrecognized device in Russia. For your security, please verify your identity immediately by clicking the link below.\n\n[VERIFY ACCOUNT - SUSPICIOUS LINK]\n\nIf you do not verify within 24 hours, your account will be permanently suspended.\n\nSecurity Team\n(This is a simulated phishing email for demo purposes)'
                },
                {
                    'id': 'demo-email-2',
                    'subject': 'Q2 Performance Review - Action Required',
                    'sender': '<EMAIL>',
                    'date': '2024-05-23 09:15',
                    'snippet': 'Your Q2 performance review has been scheduled. Please complete your self-assessment by Friday and prepare your project highlights...',
                    'category': 'professional',
                    'confidence': 0.91,
                    'keywords': ['performance', 'review', 'assessment', 'scheduled', 'highlights'],
                    'body': 'Dear Team Member,\n\nYour Q2 performance review has been scheduled for next Tuesday at 2:00 PM. Please:\n\n1. Complete your self-assessment form\n2. Prepare your project highlights\n3. Review your goals from Q1\n\nThe meeting will be held in Conference Room B. If you need to reschedule, please contact HR.\n\nBest regards,\nHR Team'
                },
                {
                    'id': 'demo-email-3',
                    'subject': 'Re: Ticket #CYB-45678 - Network Security Issue Resolved',
                    'sender': '<EMAIL>',
                    'date': '2024-05-22 16:45',
                    'snippet': 'Good news! We have successfully resolved the network security issue you reported. The firewall has been updated and all systems are secure...',
                    'category': 'professional',
                    'confidence': 0.96,
                    'keywords': ['ticket', 'security', 'resolved', 'firewall', 'systems'],
                    'body': 'Hello,\n\nGood news! We have successfully resolved the network security issue you reported in ticket #CYB-45678.\n\nActions taken:\n- Updated firewall rules\n- Patched security vulnerabilities\n- Implemented additional monitoring\n\nAll systems are now secure and functioning normally. Please let us know if you experience any further issues.\n\nBest regards,\nCybersecurity Support Team'
                },
                {
                    'id': 'demo-email-4',
                    'subject': 'Coffee catch-up this weekend? ☕',
                    'sender': '<EMAIL>',
                    'date': '2024-05-21 18:22',
                    'snippet': 'Hey! Hope you\'re doing well. Want to grab coffee this Saturday? I found this cool new cyberpunk-themed café downtown...',
                    'category': 'casual',
                    'confidence': 0.93,
                    'keywords': ['coffee', 'weekend', 'saturday', 'café', 'downtown'],
                    'body': 'Hey!\n\nHope you\'re doing well! I was thinking we could grab coffee this Saturday around 2 PM. I found this really cool cyberpunk-themed café downtown called "Neural Café" - thought you\'d love the aesthetic!\n\nLet me know if you\'re free. We can catch up on everything that\'s been happening.\n\nCheers,\nAlex'
                },
                {
                    'id': 'demo-email-5',
                    'subject': '💰 FINAL HOURS: 90% OFF CRYPTO INVESTMENT OPPORTUNITY!',
                    'sender': '<EMAIL>',
                    'date': '2024-05-20 10:05',
                    'snippet': 'Don\'t miss the investment opportunity of a lifetime! Our exclusive crypto algorithm guarantees 500% returns in 24 hours...',
                    'category': 'spam',
                    'confidence': 0.97,
                    'keywords': ['investment', 'crypto', 'returns', 'exclusive', 'opportunity'],
                    'body': 'EXCLUSIVE INVESTMENT OPPORTUNITY!\n\nDon\'t miss out on the investment opportunity of a lifetime! Our revolutionary crypto trading algorithm guarantees 500% returns in just 24 hours.\n\n✅ Guaranteed profits\n✅ No risk involved\n✅ Limited time offer\n✅ Join thousands of successful investors\n\nInvest now before this offer expires!\n\n[INVEST NOW - SUSPICIOUS LINK]\n\n(This is a simulated scam email for demo purposes)'
                },
                {
                    'id': 'demo-email-6',
                    'subject': 'System Maintenance Scheduled - Sunday 2AM',
                    'sender': '<EMAIL>',
                    'date': '2024-05-19 15:30',
                    'snippet': 'Scheduled maintenance window for our email classification system this Sunday from 2:00 AM to 4:00 AM EST...',
                    'category': 'professional',
                    'confidence': 0.89,
                    'keywords': ['maintenance', 'scheduled', 'system', 'sunday', 'window'],
                    'body': 'Dear Team,\n\nWe have scheduled a maintenance window for our email classification system:\n\nDate: Sunday, May 21st\nTime: 2:00 AM - 4:00 AM EST\nDuration: Approximately 2 hours\n\nDuring this time:\n- Email classification may be temporarily unavailable\n- System performance may be affected\n- All data will be preserved\n\nWe apologize for any inconvenience.\n\nIT Operations Team'
                }
            ]

            # Save demo emails to database if user is logged in
            if 'user_id' in session:
                try:
                    user_id = session['user_id']
                    for email in demo_emails:
                        save_email(email, user_id)

                    # Update statistics
                    update_statistics(user_id)
                except Exception as db_error:
                    logger.error(f"Error saving demo emails to database: {str(db_error)}")

            return jsonify(demo_emails)

        # Check if authenticated
        gmail_service = None
        if 'credentials' in session:
            gmail_service, _ = get_gmail_service()
        else:
            # Return empty list if not authenticated
            return jsonify([])

        # Get query parameters
        max_results = request.args.get('max_results', 10, type=int)
        query = request.args.get('query', '')

        # Check if we should use database or fetch from Gmail
        use_database = request.args.get('use_database', 'true').lower() == 'true'

        if use_database and 'user_id' in session:
            # Get emails from database
            db_emails = get_emails(
                user_id=session['user_id'],
                limit=max_results,
                category=request.args.get('category')
            )
            return jsonify(db_emails)

        # Fetch emails from Gmail
        emails = fetch_emails(gmail_service, max_results=max_results, query=query)

        if not emails:
            return jsonify([])

        # Initialize email analyzer
        email_analyzer = EmailAnalyzer()

        # Analyze emails
        analyzed_emails = []
        for email in emails:
            analysis = email_analyzer.analyze_email(email)

            analyzed_email = {
                'id': email['id'],
                'thread_id': email.get('thread_id', ''),
                'subject': email['subject'],
                'sender': email['sender'],
                'recipient': email.get('recipient', ''),
                'date': email['date'],
                'snippet': email.get('snippet', ''),
                'body': email.get('body', ''),
                'category': analysis['category'],
                'confidence': analysis['confidence'],
                'keywords': analysis['keywords'],
                'important': email.get('important', False),
                'unread': email.get('unread', False),
                'gmail_category': email.get('category', 'NONE')
            }

            analyzed_emails.append(analyzed_email)

            # Save to database if user is logged in
            if 'user_id' in session:
                try:
                    save_email(analyzed_email, session['user_id'])
                except Exception as db_error:
                    logger.error(f"Error saving email to database: {str(db_error)}")

        # Update statistics
        if 'user_id' in session:
            try:
                update_statistics(session['user_id'])
            except Exception as stats_error:
                logger.error(f"Error updating statistics: {str(stats_error)}")

        return jsonify(analyzed_emails)

    except Exception as e:
        logger.error(f"Error fetching emails: {str(e)}")
        return jsonify({'error': str(e)}), 500
