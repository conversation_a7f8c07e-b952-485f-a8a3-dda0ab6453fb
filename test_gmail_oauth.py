#!/usr/bin/env python3
"""
Gmail OAuth Test Script
Tests if the OAuth setup is working correctly
"""

import os
import json
import sys
from pathlib import Path

def test_credentials_file():
    """Test if credentials file exists and is valid."""
    print("🔍 Testing credentials file...")
    
    cred_path = Path("config/client_secret.json")
    
    if not cred_path.exists():
        print("❌ Credentials file not found: config/client_secret.json")
        return False
    
    try:
        with open(cred_path, 'r') as f:
            config = json.load(f)
        
        web_config = config.get('web', {})
        client_id = web_config.get('client_id', '')
        client_secret = web_config.get('client_secret', '')
        redirect_uris = web_config.get('redirect_uris', [])
        
        # Check for template values
        if 'YOUR_CLIENT_ID' in client_id:
            print("❌ Client ID contains template value")
            return False
        
        if 'YOUR_CLIENT_SECRET' in client_secret:
            print("❌ Client Secret contains template value")
            return False
        
        # Check redirect URI
        expected_uri = "http://localhost:8080/oauth2callback"
        if expected_uri not in redirect_uris:
            print(f"⚠️  Warning: Expected redirect URI not found")
            print(f"   Expected: {expected_uri}")
            print(f"   Found: {redirect_uris}")
            return False
        
        print("✅ Credentials file is valid!")
        print(f"   Client ID: {client_id[:30]}...")
        print(f"   Project ID: {web_config.get('project_id', 'N/A')}")
        print(f"   Redirect URI: {expected_uri}")
        return True
        
    except Exception as e:
        print(f"❌ Error reading credentials: {e}")
        return False

def test_oauth_imports():
    """Test if OAuth dependencies are available."""
    print("\n🔍 Testing OAuth dependencies...")
    
    try:
        from google.auth.transport.requests import Request
        from google.oauth2.credentials import Credentials
        from google_auth_oauthlib.flow import Flow
        from googleapiclient.discovery import build
        print("✅ All OAuth dependencies available!")
        return True
    except ImportError as e:
        print(f"❌ Missing OAuth dependency: {e}")
        print("   Run: pip install -r requirements.txt")
        return False

def test_application_config():
    """Test if application is configured correctly."""
    print("\n🔍 Testing application configuration...")
    
    try:
        # Add project root to path
        sys.path.insert(0, '.')
        
        from app import create_app
        app = create_app()
        
        redirect_uri = app.config.get('OAUTH_REDIRECT_URI')
        expected_uri = 'http://localhost:8080/oauth2callback'
        
        if redirect_uri != expected_uri:
            print(f"⚠️  Application redirect URI mismatch")
            print(f"   Expected: {expected_uri}")
            print(f"   Found: {redirect_uri}")
            return False
        
        print("✅ Application configuration correct!")
        print(f"   Redirect URI: {redirect_uri}")
        return True
        
    except Exception as e:
        print(f"❌ Error testing application: {e}")
        return False

def main():
    """Main test function."""
    print("=" * 50)
    print("🧪 GMAIL OAUTH TEST SUITE")
    print("=" * 50)
    
    all_tests_passed = True
    
    # Test credentials file
    if not test_credentials_file():
        all_tests_passed = False
    
    # Test dependencies
    if not test_oauth_imports():
        all_tests_passed = False
    
    # Test application config
    if not test_application_config():
        all_tests_passed = False
    
    print("\n" + "=" * 50)
    
    if all_tests_passed:
        print("🎉 ALL TESTS PASSED!")
        print("\n✅ Your Gmail OAuth setup is ready!")
        print("\n🚀 Next steps:")
        print("   1. Start your application: python run_app.py")
        print("   2. Go to: http://localhost:8080")
        print("   3. Click 'Google OAuth' button")
        print("   4. Complete the OAuth flow")
        print("   5. Enjoy real Gmail integration!")
    else:
        print("❌ SOME TESTS FAILED!")
        print("\n🔧 Please fix the issues above and run the test again.")
        print("   Run: python test_gmail_oauth.py")

if __name__ == "__main__":
    main()
