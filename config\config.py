"""
Configuration module for the Live Email Classifier.
Handles loading of settings from JSON file or environment variables.
"""

import os
import json
import logging
from dotenv import load_dotenv
from typing import Dict, Any, Optional

# Configure basic logging first
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('email_classifier.log')
    ]
)

# Global configuration variables
EMAIL_CONFIG = {}
CLASSIFIER_CONFIG = {}
APP_CONFIG = {}
LOGGING_CONFIG = {}
MODEL_CONFIG = {}
PREPROCESSING_CONFIG = {}
DEMO_CONFIG = {}

# Settings file path
SETTINGS_FILE = 'settings.json'

def load_settings_from_json() -> bool:
    """
    Load settings from JSON file.

    Returns:
        bool: True if settings were loaded successfully, False otherwise
    """
    global EMAIL_CONFIG, CLASSIFIER_CONFIG, APP_CONFIG, LOGGING_CONFIG, MODEL_CONFIG, PREPROCESSING_CONFIG, DEMO_CONFIG

    try:
        with open(SETTINGS_FILE, 'r') as f:
            settings = json.load(f)

        # Load configurations from JSON
        EMAIL_CONFIG = settings.get('email', {})
        CLASSIFIER_CONFIG = settings.get('classifier', {})
        APP_CONFIG = settings.get('app', {})
        LOGGING_CONFIG = settings.get('logging', {})
        MODEL_CONFIG = settings.get('model', {})
        PREPROCESSING_CONFIG = settings.get('preprocessing', {})
        DEMO_CONFIG = settings.get('demo', {})

        logging.info(f"Settings loaded from {SETTINGS_FILE}")
        return True
    except FileNotFoundError:
        logging.warning(f"{SETTINGS_FILE} not found. Will use environment variables.")
        return False
    except json.JSONDecodeError as e:
        logging.error(f"Error parsing {SETTINGS_FILE}: {str(e)}")
        return False
    except Exception as e:
        logging.error(f"Error loading settings from {SETTINGS_FILE}: {str(e)}")
        return False

def load_settings_from_env():
    """Load settings from environment variables."""
    global EMAIL_CONFIG, CLASSIFIER_CONFIG, APP_CONFIG

    # Load environment variables from .env file
    load_dotenv()

    # Email configuration
    EMAIL_CONFIG = {
        'email_address': os.getenv('EMAIL_ADDRESS', ''),
        'password': os.getenv('EMAIL_PASSWORD', ''),
        'imap_server': os.getenv('IMAP_SERVER', 'imap.gmail.com'),
        'imap_port': int(os.getenv('IMAP_PORT', '993')),
        'fetch_limit': int(os.getenv('FETCH_LIMIT', '10')),
        'check_interval': int(os.getenv('CHECK_INTERVAL', '60')),  # seconds
        'unread_only': os.getenv('UNREAD_ONLY', 'True').lower() == 'true',
        'mailbox': os.getenv('MAILBOX', 'INBOX')
    }

    # Classifier configuration
    CLASSIFIER_CONFIG = {
        'model_path': os.getenv('MODEL_PATH', 'data/model/email_classifier.pkl'),
        'vectorizer_path': os.getenv('VECTORIZER_PATH', 'data/model/vectorizer.pkl'),
        'training_data': os.getenv('TRAINING_DATA', 'data/training_data.csv'),
        'categories': ['Spam', 'HR', 'Support'],  # Default categories
        'confidence_threshold': float(os.getenv('CONFIDENCE_THRESHOLD', '0.7'))
    }

    # Application configuration
    APP_CONFIG = {
        'debug_mode': os.getenv('DEBUG_MODE', 'False').lower() == 'true',
        'auto_apply_labels': os.getenv('AUTO_APPLY_LABELS', 'True').lower() == 'true',
        'log_level': os.getenv('LOG_LEVEL', 'INFO')
    }

    # Set default values for other configurations
    global LOGGING_CONFIG, MODEL_CONFIG, PREPROCESSING_CONFIG, DEMO_CONFIG

    LOGGING_CONFIG = {
        'log_file': 'email_classifier.log',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'console_output': True
    }

    MODEL_CONFIG = {
        'algorithm': 'naive_bayes',
        'test_size': 0.2,
        'random_state': 42,
        'max_features': 5000
    }

    PREPROCESSING_CONFIG = {
        'remove_stopwords': True,
        'stemming': True,
        'min_word_length': 3,
        'language': 'english'
    }

    DEMO_CONFIG = {
        'enabled': True,
        'sample_texts': [
            "Warning: Your email account will be locked unless you verify your identity now.",
            "Please review the updated company vacation policy for the upcoming summer.",
            "We have received your technical issue report and are working on a solution."
        ]
    }

# Initialize settings - try JSON first, fall back to environment variables
if not load_settings_from_json():
    load_settings_from_env()

# Configure logging based on settings
log_level = getattr(logging, APP_CONFIG.get('log_level', 'INFO'))
logging.getLogger().setLevel(log_level)

def validate_config() -> bool:
    """
    Validate that all required configuration values are present.

    Returns:
        bool: True if configuration is valid, False otherwise
    """
    # Check classifier configuration
    if not os.path.exists(os.path.dirname(CLASSIFIER_CONFIG['model_path'])):
        try:
            os.makedirs(os.path.dirname(CLASSIFIER_CONFIG['model_path']), exist_ok=True)
        except Exception as e:
            logging.error(f"Failed to create model directory: {str(e)}")
            return False

    # Check email configuration - just log a warning if missing
    if not EMAIL_CONFIG['email_address'] or not EMAIL_CONFIG['password']:
        logging.warning("Email address and password not provided. Running in demo mode only.")

    return True

def get_config() -> Dict[str, Dict[str, Any]]:
    """
    Get the complete configuration.

    Returns:
        Dict[str, Dict[str, Any]]: Complete configuration dictionary
    """
    return {
        'email': EMAIL_CONFIG,
        'classifier': CLASSIFIER_CONFIG,
        'app': APP_CONFIG,
        'logging': LOGGING_CONFIG,
        'model': MODEL_CONFIG,
        'preprocessing': PREPROCESSING_CONFIG,
        'demo': DEMO_CONFIG
    }

def update_config(section: str, key: str, value: Any) -> bool:
    """
    Update a configuration value.

    Args:
        section: Configuration section ('email', 'classifier', 'app', etc.)
        key: Configuration key to update
        value: New value

    Returns:
        bool: True if update was successful, False otherwise
    """
    global EMAIL_CONFIG, CLASSIFIER_CONFIG, APP_CONFIG, LOGGING_CONFIG, MODEL_CONFIG, PREPROCESSING_CONFIG, DEMO_CONFIG

    if section == 'email' and key in EMAIL_CONFIG:
        EMAIL_CONFIG[key] = value
        return True
    elif section == 'classifier' and key in CLASSIFIER_CONFIG:
        CLASSIFIER_CONFIG[key] = value
        return True
    elif section == 'app' and key in APP_CONFIG:
        APP_CONFIG[key] = value
        return True
    elif section == 'logging' and key in LOGGING_CONFIG:
        LOGGING_CONFIG[key] = value
        return True
    elif section == 'model' and key in MODEL_CONFIG:
        MODEL_CONFIG[key] = value
        return True
    elif section == 'preprocessing' and key in PREPROCESSING_CONFIG:
        PREPROCESSING_CONFIG[key] = value
        return True
    elif section == 'demo' and key in DEMO_CONFIG:
        DEMO_CONFIG[key] = value
        return True

    return False

def save_settings_to_json() -> bool:
    """
    Save current settings to JSON file.

    Returns:
        bool: True if settings were saved successfully, False otherwise
    """
    try:
        with open(SETTINGS_FILE, 'w') as f:
            json.dump(get_config(), f, indent=4)
        logging.info(f"Settings saved to {SETTINGS_FILE}")
        return True
    except Exception as e:
        logging.error(f"Error saving settings to {SETTINGS_FILE}: {str(e)}")
        return False
